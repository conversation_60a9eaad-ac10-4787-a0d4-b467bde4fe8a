import uuid
from typing import List, Dict, Optional, Any
from qdrant_client import AsyncQdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from pydantic import BaseModel
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class QdrantConfig(BaseModel):
    host: str = "*************"
    port: int = 6333
    timeout: int = 60
    api_key: Optional[str] = None
    https: bool = False

class QdrantClient:
    def __init__(self, config: QdrantConfig):
        """Initialize Qdrant client."""
        self.config = config
        self.client = AsyncQdrantClient(
            host=config.host,
            port=config.port,
            timeout=config.timeout,
            api_key=config.api_key,
            https=config.https
        )
        logger.debug(f"Qdrant client initialized for {config.host}:{config.port}")

    async def create_collection(self, collection_name: str, vector_size: int = 1536, distance: Distance = Distance.COSINE):
        """Create a new collection if it doesn't exist."""
        try:
            collections = await self.client.get_collections()
            collection_exists = any(col.name == collection_name for col in collections.collections)
            
            if not collection_exists:
                await self.client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=vector_size,
                        distance=distance
                    )
                )
                logger.info(f"Created Qdrant collection: {collection_name}")
                return True
            else:
                logger.debug(f"Collection {collection_name} already exists")
                return False
                
        except Exception as e:
            logger.error(f"Error creating collection {collection_name}: {e}")
            raise

    async def upsert_documents(self, collection_name: str, documents: List[Dict], vectors: List[List[float]]):
        """Insert or update documents in a collection."""
        try:
            if len(documents) != len(vectors):
                raise ValueError("Number of documents must match number of vectors")
            
            points = []
            for doc, vector in zip(documents, vectors):
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=vector,
                    payload=doc
                )
                points.append(point)
            
            await self.client.upsert(
                collection_name=collection_name,
                points=points
            )
            
            logger.info(f"Upserted {len(points)} documents to collection {collection_name}")
            return len(points)
            
        except Exception as e:
            logger.error(f"Error upserting documents to {collection_name}: {e}")
            raise

    async def search(self, collection_name: str, query_vector: List[float], limit: int = 5, 
                    filter_conditions: Optional[Filter] = None, with_payload: bool = True, 
                    with_vectors: bool = False):
        """Search for similar vectors in a collection."""
        try:
            search_results = await self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                limit=limit,
                query_filter=filter_conditions,
                with_payload=with_payload,
                with_vectors=with_vectors
            )
            
            logger.debug(f"Found {len(search_results)} results in collection {collection_name}")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching in collection {collection_name}: {e}")
            raise

    async def search_by_text(self, collection_name: str, text_query: str, limit: int = 5, 
                           text_field: str = "text"):
        """Search for documents containing specific text (requires text indexing)."""
        try:
            # Create a filter for text matching
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key=text_field,
                        match=MatchValue(value=text_query)
                    )
                ]
            )
            
            # Use a dummy vector for text search (in practice, you'd use a text embedding)
            dummy_vector = [0.0] * 1536
            
            search_results = await self.search(
                collection_name=collection_name,
                query_vector=dummy_vector,
                limit=limit,
                filter_conditions=filter_condition,
                with_payload=True,
                with_vectors=False
            )
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error in text search for collection {collection_name}: {e}")
            raise

    async def get_collection_info(self, collection_name: str):
        """Get information about a collection."""
        try:
            collection_info = await self.client.get_collection(collection_name)
            return collection_info
        except Exception as e:
            logger.error(f"Error getting collection info for {collection_name}: {e}")
            raise

    async def delete_collection(self, collection_name: str):
        """Delete a collection."""
        try:
            await self.client.delete_collection(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting collection {collection_name}: {e}")
            raise

    async def count_documents(self, collection_name: str) -> int:
        """Count documents in a collection."""
        try:
            collection_info = await self.get_collection_info(collection_name)
            return collection_info.points_count
        except Exception as e:
            logger.error(f"Error counting documents in {collection_name}: {e}")
            return 0

    async def scroll_documents(self, collection_name: str, limit: int = 10, offset: Optional[str] = None,
                              filter_conditions: Optional[Filter] = None, with_payload: bool = True,
                              with_vectors: bool = False):
        """Scroll through documents in a collection with pagination."""
        try:
            scroll_result = await self.client.scroll(
                collection_name=collection_name,
                limit=limit,
                offset=offset,
                scroll_filter=filter_conditions,
                with_payload=with_payload,
                with_vectors=with_vectors
            )

            logger.debug(f"Scrolled {len(scroll_result[0])} documents from collection {collection_name}")
            return scroll_result  # Returns (points, next_page_offset)

        except Exception as e:
            logger.error(f"Error scrolling collection {collection_name}: {e}")
            raise

    async def delete_points(self, collection_name: str, point_ids: List[str]):
        """Delete specific points from a collection."""
        try:
            await self.client.delete(
                collection_name=collection_name,
                points_selector=point_ids
            )
            logger.info(f"Deleted {len(point_ids)} points from collection {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting points from {collection_name}: {e}")
            raise

    async def close(self):
        """Close the Qdrant client connection."""
        try:
            await self.client.close()
            logger.debug("Qdrant client connection closed")
        except Exception as e:
            logger.error(f"Error closing Qdrant client: {e}")

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            # Note: This won't work with async, but it's here for completeness
            pass
        except Exception:
            pass
