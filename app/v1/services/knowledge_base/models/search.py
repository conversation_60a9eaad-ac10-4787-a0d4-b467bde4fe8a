from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class SearchAllRequest(BaseModel):
    """Request model for paginated search across all documents."""
    query: Optional[str] = Field(None, description="Search query text")
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(10, ge=1, le=100, description="Number of results per page")
    offset: Optional[str] = Field(None, description="Pagination offset token")
    include_vectors: bool = Field(False, description="Include vector embeddings in response")
    filter_by_document: Optional[str] = Field(None, description="Filter by specific document ID")
    filter_by_type: Optional[str] = Field(None, description="Filter by document type")
    sort_by: Optional[str] = Field("relevance", description="Sort by: relevance, date, filename")

class SearchResult(BaseModel):
    """Individual search result."""
    id: str = Field(..., description="Unique chunk ID")
    score: float = Field(..., description="Relevance score")
    text: str = Field(..., description="Text content")
    document_id: str = Field(..., description="Source document ID")
    filename: str = Field(..., description="Source filename")
    page_number: Optional[int] = Field(None, description="Page number in document")
    chunk_index: int = Field(..., description="Chunk index within document")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    vector: Optional[List[float]] = Field(None, description="Vector embedding if requested")

class SearchAllResponse(BaseModel):
    """Response model for paginated search."""
    results: List[SearchResult] = Field(..., description="Search results")
    total_count: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Results per page")
    total_pages: int = Field(..., description="Total number of pages")
    next_offset: Optional[str] = Field(None, description="Offset for next page")
    has_more: bool = Field(..., description="Whether more results are available")
    query: Optional[str] = Field(None, description="Original search query")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")

class DocumentFilter(BaseModel):
    """Filter options for document search."""
    document_ids: Optional[List[str]] = Field(None, description="Filter by document IDs")
    document_types: Optional[List[str]] = Field(None, description="Filter by document types")
    date_from: Optional[datetime] = Field(None, description="Filter documents from this date")
    date_to: Optional[datetime] = Field(None, description="Filter documents to this date")
    min_score: Optional[float] = Field(None, description="Minimum relevance score")
