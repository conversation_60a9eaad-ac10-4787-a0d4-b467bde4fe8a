{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/httpClient.ts"], "sourcesContent": ["import { store } from '@/lib/redux/store';\n\ninterface RequestOptions extends RequestInit {\n  params?: Record<string, string | number | boolean | undefined>;\n  requiresAuth?: boolean;\n}\n\nclass HttpClient {\n  private baseURL: string;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n  }\n\n  private getAuthHeaders(): HeadersInit {\n    const state = store.getState();\n    const token = state.auth?.token;\n    \n    return token ? { 'Authorization': `Bearer ${token}` } : {};\n  }\n\n  private buildUrl(endpoint: string, params?: Record<string, string | number | boolean | undefined>): string {\n    const url = new URL(`${this.baseURL}${endpoint}`);\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          url.searchParams.append(key, String(value));\n        }\n      });\n    }\n    \n    return url.toString();\n  }\n\n  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {\n    const { params, requiresAuth = true, ...fetchOptions } = options;\n    \n    const url = this.buildUrl(endpoint, params);\n    \n    const headers = new Headers({\n      'Content-Type': 'application/json',\n      ...fetchOptions.headers,\n      ...(requiresAuth ? this.getAuthHeaders() : {})\n    });\n\n    const response = await fetch(url, {\n      ...fetchOptions,\n      headers\n    });\n\n    // Handle 401 Unauthorized - could trigger logout or token refresh\n    if (response.status === 401) {\n      console.error('Authentication error: Unauthorized');\n      // Could dispatch a logout action here\n      // store.dispatch(logout());\n    }\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.detail || `Request failed with status ${response.status}`);\n    }\n\n    // For 204 No Content\n    if (response.status === 204) {\n      return {} as T;\n    }\n\n    return await response.json();\n  }\n\n  async get<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, { ...options, method: 'GET' });\n  }\n\n  async post<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  async patch<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  async delete<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, { ...options, method: 'DELETE' });\n  }\n\n  // Method for file uploads\n  async uploadFile<T>(endpoint: string, file: File, options: RequestOptions = {}): Promise<T> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'POST',\n      body: formData,\n      headers: {} // Let the browser set the content type with boundary\n    });\n  }\n\n  // Method for multiple file uploads\n  async uploadFiles<T>(endpoint: string, files: File[], options: RequestOptions = {}): Promise<T> {\n    const formData = new FormData();\n    files.forEach((file, index) => {\n      formData.append(`file${index}`, file);\n    });\n    \n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'POST',\n      body: formData,\n      headers: {} // Let the browser set the content type with boundary\n    });\n  }\n}\n\n// Create and export a singleton instance\nexport const httpClient = new HttpClient(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1');\n\nexport default HttpClient;\n"], "names": [], "mappings": ";;;;AAqIyC;;AArIzC;;;AAOA,MAAM;IAOI,iBAA8B;YAEtB;QADd,MAAM,QAAQ,+HAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,MAAM,SAAQ,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,KAAK;QAE/B,OAAO,QAAQ;YAAE,iBAAiB,AAAC,UAAe,OAAN;QAAQ,IAAI,CAAC;IAC3D;IAEQ,SAAS,QAAgB,EAAE,MAA8D,EAAU;QACzG,MAAM,MAAM,IAAI,IAAI,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAEtC,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO;gBACtC;YACF;QACF;QAEA,OAAO,IAAI,QAAQ;IACrB;IAEA,MAAM,QAAW,QAAgB,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC5D,MAAM,EAAE,MAAM,EAAE,eAAe,IAAI,EAAE,GAAG,cAAc,GAAG;QAEzD,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU;QAEpC,MAAM,UAAU,IAAI,QAAQ;YAC1B,gBAAgB;YAChB,GAAG,aAAa,OAAO;YACvB,GAAI,eAAe,IAAI,CAAC,cAAc,KAAK,CAAC,CAAC;QAC/C;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,YAAY;YACf;QACF;QAEA,kEAAkE;QAClE,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,KAAK,CAAC;QACd,sCAAsC;QACtC,4BAA4B;QAC9B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,AAAC,8BAA6C,OAAhB,SAAS,MAAM;QACnF;QAEA,qBAAqB;QACrB,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,OAAO,CAAC;QACV;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B;IAEA,MAAM,IAAO,QAAgB,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACxD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAC/D;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACrE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACpE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,MAAS,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACtE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IAClE;IAEA,0BAA0B;IAC1B,MAAM,WAAc,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC3E,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM;YACN,SAAS,CAAC,EAAE,qDAAqD;QACnE;IACF;IAEA,mCAAmC;IACnC,MAAM,YAAe,QAAgB,EAAE,KAAa,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC/E,MAAM,WAAW,IAAI;QACrB,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,SAAS,MAAM,CAAC,AAAC,OAAY,OAAN,QAAS;QAClC;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM;YACN,SAAS,CAAC,EAAE,qDAAqD;QACnE;IACF;IAvHA,YAAY,OAAe,CAAE;QAF7B,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG;IACjB;AAsHF;AAGO,MAAM,aAAa,IAAI,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;uCAE7D", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/authService.ts"], "sourcesContent": ["import { httpClient } from './httpClient';\n\n// Types\ninterface LoginCredentials {\n  username: string;\n  password: string;\n}\n\ninterface LoginResponse {\n  access_token: string;\n  token_type: string;\n  user: User;\n}\n\ninterface User {\n  id: string;\n  username: string;\n  email?: string;\n}\n\nclass AuthService {\n  async login(credentials: LoginCredentials): Promise<{ user: User; token: string }> {\n    const formData = new FormData();\n    formData.append('username', credentials.username);\n    formData.append('password', credentials.password);\n\n    const response = await httpClient.request<LoginResponse>('/auth/login', {\n      method: 'POST',\n      body: formData,\n      headers: {}, // Let browser set content-type for form data\n      requiresAuth: false\n    });\n\n    return {\n      user: response.user,\n      token: response.access_token\n    };\n  }\n\n  async logout(): Promise<void> {\n    await httpClient.post('/auth/logout');\n  }\n\n  async getCurrentUser(): Promise<User> {\n    return httpClient.get<User>('/auth/me');\n  }\n\n  async refreshToken(): Promise<{ token: string }> {\n    const response = await httpClient.post<{ access_token: string }>('/auth/refresh');\n    return { token: response.access_token };\n  }\n\n  async changePassword(currentPassword: string, newPassword: string): Promise<void> {\n    await httpClient.post('/auth/change-password', {\n      current_password: currentPassword,\n      new_password: newPassword\n    });\n  }\n\n  async resetPassword(email: string): Promise<void> {\n    await httpClient.post('/auth/reset-password', { email }, { requiresAuth: false });\n  }\n\n  async confirmResetPassword(token: string, newPassword: string): Promise<void> {\n    await httpClient.post('/auth/confirm-reset-password', {\n      token,\n      new_password: newPassword\n    }, { requiresAuth: false });\n  }\n}\n\nexport const authService = new AuthService();\nexport default authService;\n"], "names": [], "mappings": ";;;;AAAA;;AAoBA,MAAM;IACJ,MAAM,MAAM,WAA6B,EAA0C;QACjF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,OAAO,CAAgB,eAAe;YACtE,QAAQ;YACR,MAAM;YACN,SAAS,CAAC;YACV,cAAc;QAChB;QAEA,OAAO;YACL,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,YAAY;QAC9B;IACF;IAEA,MAAM,SAAwB;QAC5B,MAAM,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAC;IACxB;IAEA,MAAM,iBAAgC;QACpC,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAO;IAC9B;IAEA,MAAM,eAA2C;QAC/C,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,IAAI,CAA2B;QACjE,OAAO;YAAE,OAAO,SAAS,YAAY;QAAC;IACxC;IAEA,MAAM,eAAe,eAAuB,EAAE,WAAmB,EAAiB;QAChF,MAAM,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,yBAAyB;YAC7C,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,KAAa,EAAiB;QAChD,MAAM,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,wBAAwB;YAAE;QAAM,GAAG;YAAE,cAAc;QAAM;IACjF;IAEA,MAAM,qBAAqB,KAAa,EAAE,WAAmB,EAAiB;QAC5E,MAAM,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,gCAAgC;YACpD;YACA,cAAc;QAChB,GAAG;YAAE,cAAc;QAAM;IAC3B;AACF;AAEO,MAAM,cAAc,IAAI;uCAChB", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/knowledgeBaseService.ts"], "sourcesContent": ["import { httpClient } from './httpClient';\nimport { store } from '@/lib/redux/store';\n\n// Types\ninterface SearchAllRequest {\n  query?: string;\n  page?: number;\n  page_size?: number;\n  document_type?: string;\n  document_id?: string;\n}\n\ninterface SearchResult {\n  id: string;\n  content: string;\n  metadata: Record<string, any>;\n  score?: number;\n}\n\ninterface SearchAllResponse {\n  results: SearchResult[];\n  total: number;\n  page: number;\n  page_size: number;\n  has_next: boolean;\n  scroll_id?: string;\n}\n\ninterface Document {\n  id: string;\n  filename: string;\n  content_type: string;\n  size: number;\n  upload_date: string;\n  metadata?: Record<string, any>;\n}\n\nclass KnowledgeBaseService {\n  // Custom upload method with progress tracking\n  private async uploadWithProgress<T>(endpoint: string, formData: FormData, onProgress?: (progress: number) => void): Promise<T> {\n    if (!onProgress) {\n      // If no progress tracking needed, use the httpClient directly\n      return httpClient.request<T>(endpoint, {\n        method: 'POST',\n        body: formData,\n        headers: {} // Let browser set content-type with boundary\n      });\n    }\n\n    // For progress tracking, we need to use XMLHttpRequest\n    return new Promise((resolve, reject) => {\n      const xhr = new XMLHttpRequest();\n\n      xhr.upload.addEventListener('progress', (e) => {\n        if (e.lengthComputable) {\n          const percentComplete = (e.loaded / e.total) * 100;\n          onProgress(percentComplete);\n        }\n      });\n\n      xhr.addEventListener('load', () => {\n        if (xhr.status >= 200 && xhr.status < 300) {\n          try {\n            const response = JSON.parse(xhr.responseText);\n            resolve(response);\n          } catch (error) {\n            reject(new Error('Invalid JSON response'));\n          }\n        } else {\n          try {\n            const errorData = JSON.parse(xhr.responseText);\n            reject(new Error(errorData.detail || `HTTP ${xhr.status}: ${xhr.statusText}`));\n          } catch (error) {\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\n          }\n        }\n      });\n\n      xhr.addEventListener('error', () => {\n        reject(new Error('Network error occurred'));\n      });\n\n      // Get the base URL\n      const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1';\n      const url = `${baseURL}${endpoint}`;\n      xhr.open('POST', url);\n\n      // Get auth token from Redux store\n      try {\n        const state = store.getState();\n        const token = state.auth?.token;\n        \n        if (token) {\n          xhr.setRequestHeader('Authorization', `Bearer ${token}`);\n        }\n      } catch (error) {\n        console.warn('Could not get auth token for upload:', error);\n      }\n\n      xhr.send(formData);\n    });\n  }\n\n  // Knowledge Base methods\n  async searchAllDocuments(params: SearchAllRequest): Promise<SearchAllResponse> {\n    return httpClient.get<SearchAllResponse>('/knowledge-base/search_all', { params });\n  }\n\n  async uploadDocument(file: File, onProgress?: (progress: number) => void): Promise<Document> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    return this.uploadWithProgress<Document>('/knowledge-base/upload', formData, onProgress);\n  }\n\n  async uploadDocuments(files: File[], onProgress?: (progress: number) => void): Promise<Document[]> {\n    const formData = new FormData();\n    files.forEach((file, index) => {\n      formData.append(`file`, file);\n    });\n    \n    return this.uploadWithProgress<Document[]>('/knowledge-base/upload-multiple', formData, onProgress);\n  }\n\n  async getDocuments(): Promise<Document[]> {\n    return httpClient.get<Document[]>('/knowledge-base/documents');\n  }\n\n  async deleteDocument(documentId: string): Promise<void> {\n    await httpClient.delete(`/knowledge-base/documents/${documentId}`);\n  }\n\n  async getDocument(documentId: string): Promise<Document> {\n    return httpClient.get<Document>(`/knowledge-base/documents/${documentId}`);\n  }\n\n  async updateDocumentMetadata(documentId: string, metadata: Record<string, any>): Promise<Document> {\n    return httpClient.patch<Document>(`/knowledge-base/documents/${documentId}`, { metadata });\n  }\n}\n\nexport const knowledgeBaseService = new KnowledgeBaseService();\nexport default knowledgeBaseService;\n"], "names": [], "mappings": ";;;;AAmFsB;AAnFtB;AACA;;;AAoCA,MAAM;IACJ,8CAA8C;IAC9C,MAAc,mBAAsB,QAAgB,EAAE,QAAkB,EAAE,UAAuC,EAAc;QAC7H,IAAI,CAAC,YAAY;YACf,8DAA8D;YAC9D,OAAO,uIAAA,CAAA,aAAU,CAAC,OAAO,CAAI,UAAU;gBACrC,QAAQ;gBACR,MAAM;gBACN,SAAS,CAAC,EAAE,6CAA6C;YAC3D;QACF;QAEA,uDAAuD;QACvD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAEhB,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;gBACvC,IAAI,EAAE,gBAAgB,EAAE;oBACtB,MAAM,kBAAkB,AAAC,EAAE,MAAM,GAAG,EAAE,KAAK,GAAI;oBAC/C,WAAW;gBACb;YACF;YAEA,IAAI,gBAAgB,CAAC,QAAQ;gBAC3B,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC7C,OAAO,IAAI,MAAM,UAAU,MAAM,IAAI,AAAC,QAAsB,OAAf,IAAI,MAAM,EAAC,MAAmB,OAAf,IAAI,UAAU;oBAC5E,EAAE,OAAO,OAAO;wBACd,OAAO,IAAI,MAAM,AAAC,QAAsB,OAAf,IAAI,MAAM,EAAC,MAAmB,OAAf,IAAI,UAAU;oBACxD;gBACF;YACF;YAEA,IAAI,gBAAgB,CAAC,SAAS;gBAC5B,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,MAAM,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;YACnD,MAAM,MAAM,AAAC,GAAY,OAAV,SAAmB,OAAT;YACzB,IAAI,IAAI,CAAC,QAAQ;YAEjB,kCAAkC;YAClC,IAAI;oBAEY;gBADd,MAAM,QAAQ,+HAAA,CAAA,QAAK,CAAC,QAAQ;gBAC5B,MAAM,SAAQ,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,KAAK;gBAE/B,IAAI,OAAO;oBACT,IAAI,gBAAgB,CAAC,iBAAiB,AAAC,UAAe,OAAN;gBAClD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,wCAAwC;YACvD;YAEA,IAAI,IAAI,CAAC;QACX;IACF;IAEA,yBAAyB;IACzB,MAAM,mBAAmB,MAAwB,EAA8B;QAC7E,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAoB,8BAA8B;YAAE;QAAO;IAClF;IAEA,MAAM,eAAe,IAAU,EAAE,UAAuC,EAAqB;QAC3F,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,CAAC,kBAAkB,CAAW,0BAA0B,UAAU;IAC/E;IAEA,MAAM,gBAAgB,KAAa,EAAE,UAAuC,EAAuB;QACjG,MAAM,WAAW,IAAI;QACrB,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,SAAS,MAAM,CAAE,QAAO;QAC1B;QAEA,OAAO,IAAI,CAAC,kBAAkB,CAAa,mCAAmC,UAAU;IAC1F;IAEA,MAAM,eAAoC;QACxC,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAa;IACpC;IAEA,MAAM,eAAe,UAAkB,EAAiB;QACtD,MAAM,uIAAA,CAAA,aAAU,CAAC,MAAM,CAAC,AAAC,6BAAuC,OAAX;IACvD;IAEA,MAAM,YAAY,UAAkB,EAAqB;QACvD,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAW,AAAC,6BAAuC,OAAX;IAC/D;IAEA,MAAM,uBAAuB,UAAkB,EAAE,QAA6B,EAAqB;QACjG,OAAO,uIAAA,CAAA,aAAU,CAAC,KAAK,CAAW,AAAC,6BAAuC,OAAX,aAAc;YAAE;QAAS;IAC1F;AACF;AAEO,MAAM,uBAAuB,IAAI;uCACzB", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/playgroundService.ts"], "sourcesContent": ["import { httpClient } from './httpClient';\n\n// Types\ninterface Message {\n  id: string;\n  content: string;\n  role: 'user' | 'assistant';\n  timestamp: string;\n  conversation_id?: string;\n}\n\ninterface Conversation {\n  id: string;\n  title: string;\n  created_at: string;\n  updated_at: string;\n  message_count: number;\n}\n\ninterface SendMessageRequest {\n  message: string;\n  conversation_id?: string;\n  context?: string;\n  temperature?: number;\n  max_tokens?: number;\n}\n\ninterface SendMessageResponse {\n  message: Message;\n  conversation_id: string;\n  usage?: {\n    prompt_tokens: number;\n    completion_tokens: number;\n    total_tokens: number;\n  };\n}\n\ninterface ChatSettings {\n  temperature: number;\n  max_tokens: number;\n  model: string;\n  system_prompt?: string;\n}\n\nclass PlaygroundService {\n  // Chat/Message methods\n  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {\n    return httpClient.post<SendMessageResponse>('/chat/message', request);\n  }\n\n  async getConversations(): Promise<Conversation[]> {\n    return httpClient.get<Conversation[]>('/chat/conversations');\n  }\n\n  async getConversation(conversationId: string): Promise<Conversation> {\n    return httpClient.get<Conversation>(`/chat/conversations/${conversationId}`);\n  }\n\n  async getConversationMessages(conversationId: string): Promise<Message[]> {\n    return httpClient.get<Message[]>(`/chat/conversations/${conversationId}/messages`);\n  }\n\n  async createConversation(title?: string): Promise<Conversation> {\n    return httpClient.post<Conversation>('/chat/conversations', { title });\n  }\n\n  async updateConversation(conversationId: string, title: string): Promise<Conversation> {\n    return httpClient.patch<Conversation>(`/chat/conversations/${conversationId}`, { title });\n  }\n\n  async deleteConversation(conversationId: string): Promise<void> {\n    await httpClient.delete(`/chat/conversations/${conversationId}`);\n  }\n\n  async deleteMessage(messageId: string): Promise<void> {\n    await httpClient.delete(`/chat/messages/${messageId}`);\n  }\n\n  // Settings methods\n  async getChatSettings(): Promise<ChatSettings> {\n    return httpClient.get<ChatSettings>('/chat/settings');\n  }\n\n  async updateChatSettings(settings: Partial<ChatSettings>): Promise<ChatSettings> {\n    return httpClient.patch<ChatSettings>('/chat/settings', settings);\n  }\n\n  // Model methods\n  async getAvailableModels(): Promise<string[]> {\n    return httpClient.get<string[]>('/chat/models');\n  }\n\n  // Stream message (for real-time chat)\n  async streamMessage(request: SendMessageRequest, onChunk: (chunk: string) => void): Promise<void> {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1'}/chat/stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${this.getAuthToken()}`\n      },\n      body: JSON.stringify(request)\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error('No response body');\n    }\n\n    const decoder = new TextDecoder();\n    \n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        \n        if (done) break;\n        \n        const chunk = decoder.decode(value);\n        const lines = chunk.split('\\n');\n        \n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') {\n              return;\n            }\n            \n            try {\n              const parsed = JSON.parse(data);\n              if (parsed.content) {\n                onChunk(parsed.content);\n              }\n            } catch (error) {\n              console.warn('Failed to parse SSE data:', data);\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  }\n\n  private getAuthToken(): string | null {\n    try {\n      const { store } = require('@/lib/redux/store');\n      const state = store.getState();\n      return state.auth?.token || null;\n    } catch (error) {\n      console.warn('Could not get auth token:', error);\n      return null;\n    }\n  }\n}\n\nexport const playgroundService = new PlaygroundService();\nexport default playgroundService;\n"], "names": [], "mappings": ";;;;AA8FoC;AA9FpC;;AA4CA,MAAM;IACJ,uBAAuB;IACvB,MAAM,YAAY,OAA2B,EAAgC;QAC3E,OAAO,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAsB,iBAAiB;IAC/D;IAEA,MAAM,mBAA4C;QAChD,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAiB;IACxC;IAEA,MAAM,gBAAgB,cAAsB,EAAyB;QACnE,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAe,AAAC,uBAAqC,OAAf;IAC7D;IAEA,MAAM,wBAAwB,cAAsB,EAAsB;QACxE,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAY,AAAC,uBAAqC,OAAf,gBAAe;IACzE;IAEA,MAAM,mBAAmB,KAAc,EAAyB;QAC9D,OAAO,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAe,uBAAuB;YAAE;QAAM;IACtE;IAEA,MAAM,mBAAmB,cAAsB,EAAE,KAAa,EAAyB;QACrF,OAAO,uIAAA,CAAA,aAAU,CAAC,KAAK,CAAe,AAAC,uBAAqC,OAAf,iBAAkB;YAAE;QAAM;IACzF;IAEA,MAAM,mBAAmB,cAAsB,EAAiB;QAC9D,MAAM,uIAAA,CAAA,aAAU,CAAC,MAAM,CAAC,AAAC,uBAAqC,OAAf;IACjD;IAEA,MAAM,cAAc,SAAiB,EAAiB;QACpD,MAAM,uIAAA,CAAA,aAAU,CAAC,MAAM,CAAC,AAAC,kBAA2B,OAAV;IAC5C;IAEA,mBAAmB;IACnB,MAAM,kBAAyC;QAC7C,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAe;IACtC;IAEA,MAAM,mBAAmB,QAA+B,EAAyB;QAC/E,OAAO,uIAAA,CAAA,aAAU,CAAC,KAAK,CAAe,kBAAkB;IAC1D;IAEA,gBAAgB;IAChB,MAAM,qBAAwC;QAC5C,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAW;IAClC;IAEA,sCAAsC;IACtC,MAAM,cAAc,OAA2B,EAAE,OAAgC,EAAiB;YAcjF;QAbf,MAAM,WAAW,MAAM,MAAM,AAAC,GAAgE,OAA9D,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,4BAA2B,iBAAe;YAC3G,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;YAC9C;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,QAA2B,OAApB,SAAS,MAAM,EAAC,MAAwB,OAApB,SAAS,UAAU;QACjE;QAEA,MAAM,UAAS,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,SAAS;QACvC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QAEpB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBAEzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;gBAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBAEA,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC;4BAC1B,IAAI,OAAO,OAAO,EAAE;gCAClB,QAAQ,OAAO,OAAO;4BACxB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,IAAI,CAAC,6BAA6B;wBAC5C;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;IACF;IAEQ,eAA8B;QACpC,IAAI;gBAGK;YAFP,MAAM,EAAE,KAAK,EAAE;YACf,MAAM,QAAQ,MAAM,QAAQ;YAC5B,OAAO,EAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,KAAK,KAAI;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,6BAA6B;YAC1C,OAAO;QACT;IACF;AACF;AAEO,MAAM,oBAAoB,IAAI;uCACtB", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/dashboardService.ts"], "sourcesContent": ["import { httpClient } from './httpClient';\n\n// Types\ninterface DashboardStats {\n  total_documents: number;\n  total_conversations: number;\n  total_messages: number;\n  storage_used: number;\n  storage_limit: number;\n  recent_activity_count: number;\n}\n\ninterface ActivityItem {\n  id: string;\n  type: 'document_upload' | 'conversation_created' | 'message_sent' | 'document_deleted';\n  title: string;\n  description: string;\n  timestamp: string;\n  metadata?: Record<string, any>;\n}\n\ninterface UsageStats {\n  date: string;\n  documents_uploaded: number;\n  messages_sent: number;\n  conversations_created: number;\n}\n\ninterface SystemHealth {\n  status: 'healthy' | 'warning' | 'error';\n  database_status: 'connected' | 'disconnected';\n  vector_db_status: 'connected' | 'disconnected';\n  api_response_time: number;\n  uptime: number;\n}\n\ninterface UserPreferences {\n  theme: 'light' | 'dark' | 'system';\n  language: string;\n  notifications_enabled: boolean;\n  email_notifications: boolean;\n  auto_save: boolean;\n}\n\nclass DashboardService {\n  // Stats methods\n  async getDashboardStats(): Promise<DashboardStats> {\n    return httpClient.get<DashboardStats>('/dashboard/stats');\n  }\n\n  async getRecentActivity(limit: number = 10): Promise<ActivityItem[]> {\n    return httpClient.get<ActivityItem[]>('/dashboard/activity', { \n      params: { limit } \n    });\n  }\n\n  async getUsageStats(days: number = 30): Promise<UsageStats[]> {\n    return httpClient.get<UsageStats[]>('/dashboard/usage', { \n      params: { days } \n    });\n  }\n\n  async getSystemHealth(): Promise<SystemHealth> {\n    return httpClient.get<SystemHealth>('/dashboard/health');\n  }\n\n  // User preferences\n  async getUserPreferences(): Promise<UserPreferences> {\n    return httpClient.get<UserPreferences>('/dashboard/preferences');\n  }\n\n  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {\n    return httpClient.patch<UserPreferences>('/dashboard/preferences', preferences);\n  }\n\n  // Export/Import methods\n  async exportData(format: 'json' | 'csv' = 'json'): Promise<Blob> {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1'}/dashboard/export`, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${this.getAuthToken()}`,\n        'Accept': format === 'json' ? 'application/json' : 'text/csv'\n      }\n    });\n\n    if (!response.ok) {\n      throw new Error(`Export failed: ${response.statusText}`);\n    }\n\n    return response.blob();\n  }\n\n  async importData(file: File): Promise<{ success: boolean; message: string }> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    return httpClient.request<{ success: boolean; message: string }>('/dashboard/import', {\n      method: 'POST',\n      body: formData,\n      headers: {} // Let browser set content-type with boundary\n    });\n  }\n\n  // Backup methods\n  async createBackup(): Promise<{ backup_id: string; message: string }> {\n    return httpClient.post<{ backup_id: string; message: string }>('/dashboard/backup');\n  }\n\n  async getBackups(): Promise<Array<{ id: string; created_at: string; size: number }>> {\n    return httpClient.get<Array<{ id: string; created_at: string; size: number }>>('/dashboard/backups');\n  }\n\n  async downloadBackup(backupId: string): Promise<Blob> {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1'}/dashboard/backups/${backupId}/download`, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${this.getAuthToken()}`\n      }\n    });\n\n    if (!response.ok) {\n      throw new Error(`Backup download failed: ${response.statusText}`);\n    }\n\n    return response.blob();\n  }\n\n  async deleteBackup(backupId: string): Promise<void> {\n    await httpClient.delete(`/dashboard/backups/${backupId}`);\n  }\n\n  private getAuthToken(): string | null {\n    try {\n      const { store } = require('@/lib/redux/store');\n      const state = store.getState();\n      return state.auth?.token || null;\n    } catch (error) {\n      console.warn('Could not get auth token:', error);\n      return null;\n    }\n  }\n}\n\nexport const dashboardService = new DashboardService();\nexport default dashboardService;\n"], "names": [], "mappings": ";;;;AA6EoC;AA7EpC;;AA4CA,MAAM;IACJ,gBAAgB;IAChB,MAAM,oBAA6C;QACjD,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAiB;IACxC;IAEA,MAAM,oBAA+D;YAA7C,QAAA,iEAAgB;QACtC,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAiB,uBAAuB;YAC3D,QAAQ;gBAAE;YAAM;QAClB;IACF;IAEA,MAAM,gBAAwD;YAA1C,OAAA,iEAAe;QACjC,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAe,oBAAoB;YACtD,QAAQ;gBAAE;YAAK;QACjB;IACF;IAEA,MAAM,kBAAyC;QAC7C,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAe;IACtC;IAEA,mBAAmB;IACnB,MAAM,qBAA+C;QACnD,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAkB;IACzC;IAEA,MAAM,sBAAsB,WAAqC,EAA4B;QAC3F,OAAO,uIAAA,CAAA,aAAU,CAAC,KAAK,CAAkB,0BAA0B;IACrE;IAEA,wBAAwB;IACxB,MAAM,aAA2D;YAAhD,SAAA,iEAAyB;QACxC,MAAM,WAAW,MAAM,MAAM,AAAC,GAAgE,OAA9D,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,4BAA2B,sBAAoB;YAChH,QAAQ;YACR,SAAS;gBACP,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;gBAC5C,UAAU,WAAW,SAAS,qBAAqB;YACrD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,kBAAqC,OAApB,SAAS,UAAU;QACvD;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,IAAU,EAAkD;QAC3E,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,uIAAA,CAAA,aAAU,CAAC,OAAO,CAAwC,qBAAqB;YACpF,QAAQ;YACR,MAAM;YACN,SAAS,CAAC,EAAE,6CAA6C;QAC3D;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAgE;QACpE,OAAO,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAyC;IACjE;IAEA,MAAM,aAA+E;QACnF,OAAO,uIAAA,CAAA,aAAU,CAAC,GAAG,CAA0D;IACjF;IAEA,MAAM,eAAe,QAAgB,EAAiB;QACpD,MAAM,WAAW,MAAM,MAAM,AAAC,GAAqF,OAAnF,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,4BAA2B,uBAA8B,OAAT,UAAS,cAAY;YACtI,QAAQ;YACR,SAAS;gBACP,iBAAiB,AAAC,UAA6B,OAApB,IAAI,CAAC,YAAY;YAC9C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,2BAA8C,OAApB,SAAS,UAAU;QAChE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,QAAgB,EAAiB;QAClD,MAAM,uIAAA,CAAA,aAAU,CAAC,MAAM,CAAC,AAAC,sBAA8B,OAAT;IAChD;IAEQ,eAA8B;QACpC,IAAI;gBAGK;YAFP,MAAM,EAAE,KAAK,EAAE;YACf,MAAM,QAAQ,MAAM,QAAQ;YAC5B,OAAO,EAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,KAAK,KAAI;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,6BAA6B;YAC1C,OAAO;QACT;IACF;AACF;AAEO,MAAM,mBAAmB,IAAI;uCACrB", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/apiService.ts"], "sourcesContent": ["// Re-export all services for backward compatibility\nexport { authService } from './authService';\nexport { knowledgeBaseService } from './knowledgeBaseService';\nexport { playgroundService } from './playgroundService';\nexport { dashboardService } from './dashboardService';\n\n// Legacy API service that delegates to specific services\nimport { authService } from './authService';\nimport { knowledgeBaseService } from './knowledgeBaseService';\nimport { playgroundService } from './playgroundService';\nimport { dashboardService } from './dashboardService';\n\nclass ApiService {\n  // Auth methods\n  login = authService.login.bind(authService);\n  logout = authService.logout.bind(authService);\n  getCurrentUser = authService.getCurrentUser.bind(authService);\n\n  // Knowledge Base methods\n  searchAllDocuments = knowledgeBaseService.searchAllDocuments.bind(knowledgeBaseService);\n  uploadDocument = knowledgeBaseService.uploadDocument.bind(knowledgeBaseService);\n  uploadDocuments = knowledgeBaseService.uploadDocuments.bind(knowledgeBaseService);\n  getDocuments = knowledgeBaseService.getDocuments.bind(knowledgeBaseService);\n  deleteDocument = knowledgeBaseService.deleteDocument.bind(knowledgeBaseService);\n  getDocument = knowledgeBaseService.getDocument.bind(knowledgeBaseService);\n\n  // Playground methods\n  sendMessage = playgroundService.sendMessage.bind(playgroundService);\n  getConversations = playgroundService.getConversations.bind(playgroundService);\n  getConversation = playgroundService.getConversation.bind(playgroundService);\n  deleteConversation = playgroundService.deleteConversation.bind(playgroundService);\n\n  // Dashboard methods\n  getDashboardStats = dashboardService.getDashboardStats.bind(dashboardService);\n  getRecentActivity = dashboardService.getRecentActivity.bind(dashboardService);\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;AACpD;AACA;AACA;AACA;;;;;;;;;;AAQA,MAAM;;QACJ,eAAe;QACf,+KAAA,SAAQ,wIAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI,CAAC,wIAAA,CAAA,cAAW;QAC1C,+KAAA,UAAS,wIAAA,CAAA,cAAW,CAAC,MAAM,CAAC,IAAI,CAAC,wIAAA,CAAA,cAAW;QAC5C,+KAAA,kBAAiB,wIAAA,CAAA,cAAW,CAAC,cAAc,CAAC,IAAI,CAAC,wIAAA,CAAA,cAAW;QAE5D,yBAAyB;QACzB,+KAAA,sBAAqB,iJAAA,CAAA,uBAAoB,CAAC,kBAAkB,CAAC,IAAI,CAAC,iJAAA,CAAA,uBAAoB;QACtF,+KAAA,kBAAiB,iJAAA,CAAA,uBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,iJAAA,CAAA,uBAAoB;QAC9E,+KAAA,mBAAkB,iJAAA,CAAA,uBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,iJAAA,CAAA,uBAAoB;QAChF,+KAAA,gBAAe,iJAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,iJAAA,CAAA,uBAAoB;QAC1E,+KAAA,kBAAiB,iJAAA,CAAA,uBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,iJAAA,CAAA,uBAAoB;QAC9E,+KAAA,eAAc,iJAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,iJAAA,CAAA,uBAAoB;QAExE,qBAAqB;QACrB,+KAAA,eAAc,8IAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,8IAAA,CAAA,oBAAiB;QAClE,+KAAA,oBAAmB,8IAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,8IAAA,CAAA,oBAAiB;QAC5E,+KAAA,mBAAkB,8IAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,8IAAA,CAAA,oBAAiB;QAC1E,+KAAA,sBAAqB,8IAAA,CAAA,oBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,8IAAA,CAAA,oBAAiB;QAEhF,oBAAoB;QACpB,+KAAA,qBAAoB,6IAAA,CAAA,mBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,6IAAA,CAAA,mBAAgB;QAC5E,+KAAA,qBAAoB,6IAAA,CAAA,mBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,6IAAA,CAAA,mBAAgB;;AAC9E;AAGO,MAAM,aAAa,IAAI;uCACf", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { authService } from '@/lib/services/authService';\n\ninterface User {\n  id: string;\n  username: string;\n  email?: string;\n}\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n};\n\n// Async thunks\nexport const loginUser = createAsyncThunk(\n  'auth/login',\n  async (credentials: { username: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await apiService.login(credentials);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Login failed');\n    }\n  }\n);\n\nexport const logoutUser = createAsyncThunk(\n  'auth/logout',\n  async (_, { rejectWithValue }) => {\n    try {\n      await apiService.logout();\n      return null;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Logout failed');\n    }\n  }\n);\n\nexport const getCurrentUser = createAsyncThunk(\n  'auth/getCurrentUser',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getCurrentUser();\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to get user');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setToken: (state, action: PayloadAction<string>) => {\n      state.token = action.payload;\n      state.isAuthenticated = true;\n    },\n    clearAuth: (state) => {\n      state.user = null;\n      state.token = null;\n      state.isAuthenticated = false;\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Login\n      .addCase(loginUser.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(loginUser.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.isAuthenticated = true;\n        state.error = null;\n      })\n      .addCase(loginUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n        state.isAuthenticated = false;\n      })\n      // Logout\n      .addCase(logoutUser.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(logoutUser.fulfilled, (state) => {\n        state.isLoading = false;\n        state.user = null;\n        state.token = null;\n        state.isAuthenticated = false;\n        state.error = null;\n      })\n      .addCase(logoutUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Get current user\n      .addCase(getCurrentUser.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(getCurrentUser.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload;\n        state.isAuthenticated = true;\n        state.error = null;\n      })\n      .addCase(getCurrentUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n        state.isAuthenticated = false;\n      });\n  },\n});\n\nexport const { clearError, setToken, clearAuth } = authSlice.actions;\nexport default authSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAiBA,MAAM,eAA0B;IAC9B,MAAM;IACN,OAAO;IACP,iBAAiB;IACjB,WAAW;IACX,OAAO;AACT;AAGO,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACtC,cACA,OAAO;QAAqD,EAAE,eAAe,EAAE;IAC7E,IAAI;QACF,MAAM,WAAW,MAAM,WAAW,KAAK,CAAC;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,aAAa,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACvC,eACA,OAAO;QAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM;QACvB,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC3C,uBACA,OAAO;QAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,WAAW,cAAc;QAChD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGF,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;QAC1B;QACA,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,KAAK,GAAG;YACd,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB;IACF;IACA,eAAe,CAAC;QACd,OACE,QAAQ;SACP,OAAO,CAAC,UAAU,OAAO,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,UAAU,SAAS,EAAE,CAAC,OAAO;YACpC,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;YAChC,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YAClC,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,UAAU,QAAQ,EAAE,CAAC,OAAO;YACnC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;QAC1B,EACA,SAAS;SACR,OAAO,CAAC,WAAW,OAAO,EAAE,CAAC;YAC5B,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,WAAW,SAAS,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG;YACb,MAAM,KAAK,GAAG;YACd,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,WAAW,QAAQ,EAAE,CAAC,OAAO;YACpC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,mBAAmB;SAClB,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;QAC1B;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,UAAU,OAAO;uCACrD,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/documentsSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { apiService } from '@/lib/services/apiService';\n\ninterface Document {\n  id: string;\n  title: string;\n  filename: string;\n  type: string;\n  size: string;\n  uploadedAt: string;\n  uploadedBy: string;\n  pages: number;\n  status: 'processing' | 'processed' | 'error';\n  presignedUrl?: string;\n}\n\ninterface DocumentsState {\n  documents: Document[];\n  selectedDocument: Document | null;\n  isLoading: boolean;\n  isUploading: boolean;\n  uploadProgress: number;\n  error: string | null;\n  searchQuery: string;\n  filters: {\n    type: string;\n    status: string;\n  };\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n  };\n}\n\nconst initialState: DocumentsState = {\n  documents: [],\n  selectedDocument: null,\n  isLoading: false,\n  isUploading: false,\n  uploadProgress: 0,\n  error: null,\n  searchQuery: '',\n  filters: {\n    type: 'all',\n    status: 'all',\n  },\n  pagination: {\n    page: 1,\n    limit: 20,\n    total: 0,\n  },\n};\n\n// Async thunks\nexport const fetchDocuments = createAsyncThunk(\n  'documents/fetchDocuments',\n  async (params: { page?: number; limit?: number; search?: string }, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getDocuments(params);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch documents');\n    }\n  }\n);\n\nexport const uploadDocuments = createAsyncThunk(\n  'documents/uploadDocuments',\n  async (files: File[], { rejectWithValue, dispatch }) => {\n    try {\n      const response = await apiService.uploadDocuments(files, (progress) => {\n        dispatch(setUploadProgress(progress));\n      });\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Upload failed');\n    }\n  }\n);\n\nexport const deleteDocument = createAsyncThunk(\n  'documents/deleteDocument',\n  async (documentId: string, { rejectWithValue }) => {\n    try {\n      await apiService.deleteDocument(documentId);\n      return documentId;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to delete document');\n    }\n  }\n);\n\nexport const getDocumentDetails = createAsyncThunk(\n  'documents/getDocumentDetails',\n  async (documentId: string, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getDocument(documentId);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to get document details');\n    }\n  }\n);\n\nconst documentsSlice = createSlice({\n  name: 'documents',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setSearchQuery: (state, action: PayloadAction<string>) => {\n      state.searchQuery = action.payload;\n    },\n    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    setUploadProgress: (state, action: PayloadAction<number>) => {\n      state.uploadProgress = action.payload;\n    },\n    resetUpload: (state) => {\n      state.isUploading = false;\n      state.uploadProgress = 0;\n    },\n    setSelectedDocument: (state, action: PayloadAction<Document | null>) => {\n      state.selectedDocument = action.payload;\n    },\n    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch documents\n      .addCase(fetchDocuments.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchDocuments.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.documents = action.payload.documents;\n        state.pagination.total = action.payload.total;\n        state.error = null;\n      })\n      .addCase(fetchDocuments.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Upload documents\n      .addCase(uploadDocuments.pending, (state) => {\n        state.isUploading = true;\n        state.uploadProgress = 0;\n        state.error = null;\n      })\n      .addCase(uploadDocuments.fulfilled, (state, action) => {\n        state.isUploading = false;\n        state.uploadProgress = 100;\n        // Add new documents to the list\n        state.documents = [...action.payload.documents, ...state.documents];\n        state.error = null;\n      })\n      .addCase(uploadDocuments.rejected, (state, action) => {\n        state.isUploading = false;\n        state.uploadProgress = 0;\n        state.error = action.payload as string;\n      })\n      // Delete document\n      .addCase(deleteDocument.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(deleteDocument.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.documents = state.documents.filter(doc => doc.id !== action.payload);\n        if (state.selectedDocument?.id === action.payload) {\n          state.selectedDocument = null;\n        }\n      })\n      .addCase(deleteDocument.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Get document details\n      .addCase(getDocumentDetails.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(getDocumentDetails.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.selectedDocument = action.payload;\n      })\n      .addCase(getDocumentDetails.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const {\n  clearError,\n  setSearchQuery,\n  setFilters,\n  setUploadProgress,\n  resetUpload,\n  setSelectedDocument,\n  setPagination,\n} = documentsSlice.actions;\n\nexport default documentsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AAAA;;;AAkCA,MAAM,eAA+B;IACnC,WAAW,EAAE;IACb,kBAAkB;IAClB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,SAAS;QACP,MAAM;QACN,QAAQ;IACV;IACA,YAAY;QACV,MAAM;QACN,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC3C,4BACA,OAAO;QAA4D,EAAE,eAAe,EAAE;IACpF,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,YAAY,CAAC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC5C,6BACA,OAAO;QAAe,EAAE,eAAe,EAAE,QAAQ,EAAE;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,eAAe,CAAC,OAAO,CAAC;YACxD,SAAS,kBAAkB;QAC7B;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC3C,4BACA,OAAO;QAAoB,EAAE,eAAe,EAAE;IAC5C,IAAI;QACF,MAAM,uJAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC/C,gCACA,OAAO;QAAoB,EAAE,eAAe,EAAE;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,WAAW,CAAC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGF,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACjC,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG;gBAAE,GAAG,MAAM,OAAO;gBAAE,GAAG,OAAO,OAAO;YAAC;QACxD;QACA,mBAAmB,CAAC,OAAO;YACzB,MAAM,cAAc,GAAG,OAAO,OAAO;QACvC;QACA,aAAa,CAAC;YACZ,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;QACzB;QACA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,gBAAgB,GAAG,OAAO,OAAO;QACzC;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG;gBAAE,GAAG,MAAM,UAAU;gBAAE,GAAG,OAAO,OAAO;YAAC;QAC9D;IACF;IACA,eAAe,CAAC;QACd,OACE,kBAAkB;SACjB,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,SAAS,GAAG;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS;YAC1C,MAAM,UAAU,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YAC7C,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,mBAAmB;SAClB,OAAO,CAAC,gBAAgB,OAAO,EAAE,CAAC;YACjC,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;YACvB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;YACvB,gCAAgC;YAChC,MAAM,SAAS,GAAG;mBAAI,OAAO,OAAO,CAAC,SAAS;mBAAK,MAAM,SAAS;aAAC;YACnE,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,gBAAgB,QAAQ,EAAE,CAAC,OAAO;YACzC,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;YACvB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,kBAAkB;SACjB,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;gBAGrC;YAFJ,MAAM,SAAS,GAAG;YAClB,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO;YACzE,IAAI,EAAA,0BAAA,MAAM,gBAAgB,cAAtB,8CAAA,wBAAwB,EAAE,MAAK,OAAO,OAAO,EAAE;gBACjD,MAAM,gBAAgB,GAAG;YAC3B;QACF,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,uBAAuB;SACtB,OAAO,CAAC,mBAAmB,OAAO,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,mBAAmB,SAAS,EAAE,CAAC,OAAO;YAC7C,MAAM,SAAS,GAAG;YAClB,MAAM,gBAAgB,GAAG,OAAO,OAAO;QACzC,GACC,OAAO,CAAC,mBAAmB,QAAQ,EAAE,CAAC,OAAO;YAC5C,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACV,iBAAiB,EACjB,WAAW,EACX,mBAAmB,EACnB,aAAa,EACd,GAAG,eAAe,OAAO;uCAEX,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/chatSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { apiService } from '@/lib/services/apiService';\n\ninterface Source {\n  documentId: string;\n  filename: string;\n  pageNumber: number;\n  snippet: string;\n  confidenceScore: number;\n}\n\ninterface Message {\n  id: string;\n  type: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  sources?: Source[];\n  conversationId?: string;\n}\n\ninterface Conversation {\n  id: string;\n  title: string;\n  createdAt: Date;\n  updatedAt: Date;\n  messageCount: number;\n}\n\ninterface ChatState {\n  conversations: Conversation[];\n  currentConversation: string | null;\n  messages: Message[];\n  isLoading: boolean;\n  isTyping: boolean;\n  error: string | null;\n  searchResults: any[];\n}\n\nconst initialState: ChatState = {\n  conversations: [],\n  currentConversation: null,\n  messages: [],\n  isLoading: false,\n  isTyping: false,\n  error: null,\n  searchResults: [],\n};\n\n// Async thunks\nexport const sendMessage = createAsyncThunk(\n  'chat/sendMessage',\n  async (\n    params: { \n      message: string; \n      conversationId?: string; \n      maxResults?: number \n    }, \n    { rejectWithValue }\n  ) => {\n    try {\n      const response = await apiService.sendChatMessage(params);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to send message');\n    }\n  }\n);\n\nexport const fetchConversations = createAsyncThunk(\n  'chat/fetchConversations',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getChatConversations();\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch conversations');\n    }\n  }\n);\n\nexport const fetchChatHistory = createAsyncThunk(\n  'chat/fetchChatHistory',\n  async (conversationId: string, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getChatHistory(conversationId);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch chat history');\n    }\n  }\n);\n\nexport const deleteConversation = createAsyncThunk(\n  'chat/deleteConversation',\n  async (conversationId: string, { rejectWithValue }) => {\n    try {\n      await apiService.deleteConversation(conversationId);\n      return conversationId;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to delete conversation');\n    }\n  }\n);\n\nexport const searchInChat = createAsyncThunk(\n  'chat/searchInChat',\n  async (query: string, { rejectWithValue }) => {\n    try {\n      const response = await apiService.searchInChat(query);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Search failed');\n    }\n  }\n);\n\nconst chatSlice = createSlice({\n  name: 'chat',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setCurrentConversation: (state, action: PayloadAction<string | null>) => {\n      state.currentConversation = action.payload;\n    },\n    addUserMessage: (state, action: PayloadAction<Message>) => {\n      state.messages.push(action.payload);\n    },\n    setTyping: (state, action: PayloadAction<boolean>) => {\n      state.isTyping = action.payload;\n    },\n    clearMessages: (state) => {\n      state.messages = [];\n      state.currentConversation = null;\n    },\n    clearSearchResults: (state) => {\n      state.searchResults = [];\n    },\n    updateMessage: (state, action: PayloadAction<{ id: string; updates: Partial<Message> }>) => {\n      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload.id);\n      if (messageIndex !== -1) {\n        state.messages[messageIndex] = { ...state.messages[messageIndex], ...action.payload.updates };\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Send message\n      .addCase(sendMessage.pending, (state) => {\n        state.isTyping = true;\n        state.error = null;\n      })\n      .addCase(sendMessage.fulfilled, (state, action) => {\n        state.isTyping = false;\n        state.messages.push({\n          id: Date.now().toString(),\n          type: 'assistant',\n          content: action.payload.answer,\n          timestamp: new Date(),\n          sources: action.payload.sources,\n          conversationId: action.payload.conversationId,\n        });\n        state.currentConversation = action.payload.conversationId;\n        state.error = null;\n      })\n      .addCase(sendMessage.rejected, (state, action) => {\n        state.isTyping = false;\n        state.error = action.payload as string;\n      })\n      // Fetch conversations\n      .addCase(fetchConversations.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(fetchConversations.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.conversations = action.payload;\n      })\n      .addCase(fetchConversations.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch chat history\n      .addCase(fetchChatHistory.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(fetchChatHistory.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.messages = action.payload.map((chat: any) => [\n          {\n            id: `${chat.id}-user`,\n            type: 'user' as const,\n            content: chat.query,\n            timestamp: new Date(chat.timestamp),\n            conversationId: chat.conversationId,\n          },\n          {\n            id: `${chat.id}-assistant`,\n            type: 'assistant' as const,\n            content: chat.response,\n            timestamp: new Date(chat.timestamp),\n            sources: chat.sources,\n            conversationId: chat.conversationId,\n          },\n        ]).flat();\n      })\n      .addCase(fetchChatHistory.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Delete conversation\n      .addCase(deleteConversation.fulfilled, (state, action) => {\n        state.conversations = state.conversations.filter(conv => conv.id !== action.payload);\n        if (state.currentConversation === action.payload) {\n          state.currentConversation = null;\n          state.messages = [];\n        }\n      })\n      // Search in chat\n      .addCase(searchInChat.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(searchInChat.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.searchResults = action.payload;\n      })\n      .addCase(searchInChat.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const {\n  clearError,\n  setCurrentConversation,\n  addUserMessage,\n  setTyping,\n  clearMessages,\n  clearSearchResults,\n  updateMessage,\n} = chatSlice.actions;\n\nexport default chatSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AAAA;;;AAqCA,MAAM,eAA0B;IAC9B,eAAe,EAAE;IACjB,qBAAqB;IACrB,UAAU,EAAE;IACZ,WAAW;IACX,UAAU;IACV,OAAO;IACP,eAAe,EAAE;AACnB;AAGO,MAAM,cAAc,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACxC,oBACA,OACE;QAKA,EAAE,eAAe,EAAE;IAEnB,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,eAAe,CAAC;QAClD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC/C,2BACA,OAAO;QAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,oBAAoB;QACtD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,mBAAmB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC7C,yBACA,OAAO;QAAwB,EAAE,eAAe,EAAE;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QACjD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC/C,2BACA,OAAO;QAAwB,EAAE,eAAe,EAAE;IAChD,IAAI;QACF,MAAM,uJAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,eAAe,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACzC,qBACA,OAAO;QAAe,EAAE,eAAe,EAAE;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,YAAY,CAAC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGF,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,wBAAwB,CAAC,OAAO;YAC9B,MAAM,mBAAmB,GAAG,OAAO,OAAO;QAC5C;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,OAAO;QACpC;QACA,WAAW,CAAC,OAAO;YACjB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,eAAe,CAAC;YACd,MAAM,QAAQ,GAAG,EAAE;YACnB,MAAM,mBAAmB,GAAG;QAC9B;QACA,oBAAoB,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE;QAC1B;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,eAAe,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;YACjF,IAAI,iBAAiB,CAAC,GAAG;gBACvB,MAAM,QAAQ,CAAC,aAAa,GAAG;oBAAE,GAAG,MAAM,QAAQ,CAAC,aAAa;oBAAE,GAAG,OAAO,OAAO,CAAC,OAAO;gBAAC;YAC9F;QACF;IACF;IACA,eAAe,CAAC;QACd,OACE,eAAe;SACd,OAAO,CAAC,YAAY,OAAO,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,SAAS,EAAE,CAAC,OAAO;YACtC,MAAM,QAAQ,GAAG;YACjB,MAAM,QAAQ,CAAC,IAAI,CAAC;gBAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,SAAS,OAAO,OAAO,CAAC,MAAM;gBAC9B,WAAW,IAAI;gBACf,SAAS,OAAO,OAAO,CAAC,OAAO;gBAC/B,gBAAgB,OAAO,OAAO,CAAC,cAAc;YAC/C;YACA,MAAM,mBAAmB,GAAG,OAAO,OAAO,CAAC,cAAc;YACzD,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC,OAAO;YACrC,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,sBAAsB;SACrB,OAAO,CAAC,mBAAmB,OAAO,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,mBAAmB,SAAS,EAAE,CAAC,OAAO;YAC7C,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC,GACC,OAAO,CAAC,mBAAmB,QAAQ,EAAE,CAAC,OAAO;YAC5C,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,qBAAqB;SACpB,OAAO,CAAC,iBAAiB,OAAO,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,iBAAiB,SAAS,EAAE,CAAC,OAAO;YAC3C,MAAM,SAAS,GAAG;YAClB,MAAM,QAAQ,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAc;oBACjD;wBACE,IAAI,AAAC,GAAU,OAAR,KAAK,EAAE,EAAC;wBACf,MAAM;wBACN,SAAS,KAAK,KAAK;wBACnB,WAAW,IAAI,KAAK,KAAK,SAAS;wBAClC,gBAAgB,KAAK,cAAc;oBACrC;oBACA;wBACE,IAAI,AAAC,GAAU,OAAR,KAAK,EAAE,EAAC;wBACf,MAAM;wBACN,SAAS,KAAK,QAAQ;wBACtB,WAAW,IAAI,KAAK,KAAK,SAAS;wBAClC,SAAS,KAAK,OAAO;wBACrB,gBAAgB,KAAK,cAAc;oBACrC;iBACD,EAAE,IAAI;QACT,GACC,OAAO,CAAC,iBAAiB,QAAQ,EAAE,CAAC,OAAO;YAC1C,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,sBAAsB;SACrB,OAAO,CAAC,mBAAmB,SAAS,EAAE,CAAC,OAAO;YAC7C,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;YACnF,IAAI,MAAM,mBAAmB,KAAK,OAAO,OAAO,EAAE;gBAChD,MAAM,mBAAmB,GAAG;gBAC5B,MAAM,QAAQ,GAAG,EAAE;YACrB;QACF,EACA,iBAAiB;SAChB,OAAO,CAAC,aAAa,OAAO,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,aAAa,SAAS,EAAE,CAAC,OAAO;YACvC,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC,GACC,OAAO,CAAC,aAAa,QAAQ,EAAE,CAAC,OAAO;YACtC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EACX,UAAU,EACV,sBAAsB,EACtB,cAAc,EACd,SAAS,EACT,aAAa,EACb,kBAAkB,EAClB,aAAa,EACd,GAAG,UAAU,OAAO;uCAEN,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\ninterface Toast {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  message: string;\n  duration?: number;\n}\n\ninterface Modal {\n  id: string;\n  type: string;\n  props?: any;\n}\n\ninterface UIState {\n  theme: 'light' | 'dark';\n  sidebarOpen: boolean;\n  toasts: Toast[];\n  modals: Modal[];\n  isLoading: boolean;\n  loadingMessage: string;\n  viewMode: 'grid' | 'list';\n}\n\nconst initialState: UIState = {\n  theme: 'light',\n  sidebarOpen: false,\n  toasts: [],\n  modals: [],\n  isLoading: false,\n  loadingMessage: '',\n  viewMode: 'grid',\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n    },\n    toggleTheme: (state) => {\n      state.theme = state.theme === 'light' ? 'dark' : 'light';\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    addToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {\n      const toast: Toast = {\n        id: Date.now().toString(),\n        duration: 5000,\n        ...action.payload,\n      };\n      state.toasts.push(toast);\n    },\n    removeToast: (state, action: PayloadAction<string>) => {\n      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);\n    },\n    clearToasts: (state) => {\n      state.toasts = [];\n    },\n    openModal: (state, action: PayloadAction<Omit<Modal, 'id'>>) => {\n      const modal: Modal = {\n        id: Date.now().toString(),\n        ...action.payload,\n      };\n      state.modals.push(modal);\n    },\n    closeModal: (state, action: PayloadAction<string>) => {\n      state.modals = state.modals.filter(modal => modal.id !== action.payload);\n    },\n    closeAllModals: (state) => {\n      state.modals = [];\n    },\n    setLoading: (state, action: PayloadAction<{ isLoading: boolean; message?: string }>) => {\n      state.isLoading = action.payload.isLoading;\n      state.loadingMessage = action.payload.message || '';\n    },\n    setViewMode: (state, action: PayloadAction<'grid' | 'list'>) => {\n      state.viewMode = action.payload;\n    },\n  },\n});\n\nexport const {\n  setTheme,\n  toggleTheme,\n  setSidebarOpen,\n  toggleSidebar,\n  addToast,\n  removeToast,\n  clearToasts,\n  openModal,\n  closeModal,\n  closeAllModals,\n  setLoading,\n  setViewMode,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAyBA,MAAM,eAAwB;IAC5B,OAAO;IACP,aAAa;IACb,QAAQ,EAAE;IACV,QAAQ,EAAE;IACV,WAAW;IACX,gBAAgB;IAChB,UAAU;AACZ;AAEA,MAAM,UAAU,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACR,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QACA,aAAa,CAAC;YACZ,MAAM,KAAK,GAAG,MAAM,KAAK,KAAK,UAAU,SAAS;QACnD;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,eAAe,CAAC;YACd,MAAM,WAAW,GAAG,CAAC,MAAM,WAAW;QACxC;QACA,UAAU,CAAC,OAAO;YAChB,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,UAAU;gBACV,GAAG,OAAO,OAAO;YACnB;YACA,MAAM,MAAM,CAAC,IAAI,CAAC;QACpB;QACA,aAAa,CAAC,OAAO;YACnB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO,OAAO;QACzE;QACA,aAAa,CAAC;YACZ,MAAM,MAAM,GAAG,EAAE;QACnB;QACA,WAAW,CAAC,OAAO;YACjB,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,OAAO,OAAO;YACnB;YACA,MAAM,MAAM,CAAC,IAAI,CAAC;QACpB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO,OAAO;QACzE;QACA,gBAAgB,CAAC;YACf,MAAM,MAAM,GAAG,EAAE;QACnB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS;YAC1C,MAAM,cAAc,GAAG,OAAO,OAAO,CAAC,OAAO,IAAI;QACnD;QACA,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;IACF;AACF;AAEO,MAAM,EACX,QAAQ,EACR,WAAW,EACX,cAAc,EACd,aAAa,EACb,QAAQ,EACR,WAAW,EACX,WAAW,EACX,SAAS,EACT,UAAU,EACV,cAAc,EACd,UAAU,EACV,WAAW,EACZ,GAAG,QAAQ,OAAO;uCAEJ,QAAQ,OAAO", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/store.ts"], "sourcesContent": ["import { configureStore, combineReducers } from '@reduxjs/toolkit';\nimport { \n  persistStore, \n  persistReducer,\n  FLUSH,\n  REHYDRATE,\n  PAUSE,\n  PERSIST,\n  PURGE,\n  REGISTER\n} from 'redux-persist';\nimport storage from 'redux-persist/lib/storage';\nimport authReducer from './slices/authSlice';\nimport documentsReducer from './slices/documentsSlice';\nimport chatReducer from './slices/chatSlice';\nimport uiReducer from './slices/uiSlice';\n\nconst persistConfig = {\n  key: 'root',\n  storage,\n  whitelist: ['auth'], // only auth will be persisted\n};\n\nconst rootReducer = combineReducers({\n  auth: authReducer,\n  documents: documentsReducer,\n  chat: chatReducer,\n  ui: uiReducer,\n});\n\nconst persistedReducer = persistReducer(persistConfig, rootReducer);\n\nexport const store = configureStore({\n  reducer: persistedReducer,\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\n      },\n    }),\n});\n\nexport const persistor = persistStore(store);\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,gBAAgB;IACpB,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;IACP,WAAW;QAAC;KAAO;AACrB;AAEA,MAAM,cAAc,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,6IAAA,CAAA,UAAW;IACjB,WAAW,kJAAA,CAAA,UAAgB;IAC3B,MAAM,6IAAA,CAAA,UAAW;IACjB,IAAI,2IAAA,CAAA,UAAS;AACf;AAEA,MAAM,mBAAmB,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;AAEhD,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;IACT,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,YAAS;oBAAE,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,UAAO;oBAAE,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,WAAQ;iBAAC;YACrE;QACF;AACJ;AAEO,MAAM,YAAY,CAAA,GAAA,oMAAA,CAAA,eAAY,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/provider.tsx"], "sourcesContent": ["'use client';\n\nimport { Provider } from 'react-redux';\nimport { PersistGate } from 'redux-persist/integration/react';\nimport { store, persistor } from './store';\n\nexport function ReduxProvider({ children }: { children: React.ReactNode }) {\n  return (\n    <Provider store={store}>\n      <PersistGate loading={null} persistor={persistor}>\n        {children}\n      </PersistGate>\n    </Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;IAC5B,qBACE,6LAAC,4JAAA,CAAA,WAAQ;QAAC,OAAO,+HAAA,CAAA,QAAK;kBACpB,cAAA,6LAAC,iKAAA,CAAA,cAAW;YAAC,SAAS;YAAM,WAAW,+HAAA,CAAA,YAAS;sBAC7C;;;;;;;;;;;AAIT;KARgB", "debugId": null}}]}