{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/layout/ProtectedLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Home,\n  MessageSquare,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  User,\n  Sun,\n  Moon\n} from 'lucide-react';\n\ninterface ProtectedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [darkMode, setDarkMode] = useState(false);\n\n  const navigation = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: Home,\n      description: 'Overview & Analytics'\n    },\n    {\n      name: 'Legal Assistant',\n      href: '/legal-assistant',\n      icon: MessageSquare,\n      description: 'AI Chat & Documents'\n    },\n    {\n      name: 'Settings',\n      href: '/settings',\n      icon: Settings,\n      description: 'System Configuration'\n    }\n  ];\n\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n  };\n\n  const handleLogout = () => {\n    router.push('/login');\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${darkMode ? 'dark' : ''}`}>\n      {/* Mobile sidebar backdrop */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Desktop Layout */}\n      <div className=\"hidden lg:flex lg:h-screen lg:overflow-hidden\">\n        {/* Desktop Sidebar */}\n        <aside className=\"w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 flex-shrink-0 overflow-y-auto\">\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => router.push(item.href)}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive \n                          ? 'bg-white/20 dark:bg-gray-900/20' \n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive \n                            ? 'text-white/70 dark:text-gray-900/70' \n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </aside>\n\n        {/* Desktop Main content */}\n        <div className=\"flex-1 flex flex-col min-w-0 overflow-hidden\">\n          {/* Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm sm:px-6 lg:px-8\">\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Page content */}\n          <main className=\"flex-1 overflow-y-auto py-6 px-4 sm:px-6 lg:px-8 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n\n      {/* Mobile Layout */}\n      <div className=\"lg:hidden\">\n        {/* Mobile Sidebar */}\n        <motion.aside\n          initial={false}\n          animate={{\n            x: sidebarOpen ? 0 : -320,\n          }}\n          transition={{ type: 'spring', damping: 30, stiffness: 300 }}\n          className=\"fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 overflow-y-auto\"\n        >\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => {\n                        router.push(item.href);\n                        setSidebarOpen(false);\n                      }}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive\n                          ? 'bg-white/20 dark:bg-gray-900/20'\n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive\n                            ? 'text-white/70 dark:text-gray-900/70'\n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </motion.aside>\n\n        {/* Mobile Main content */}\n        <div className=\"min-h-screen\">\n          {/* Mobile Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm\">\n            <button\n              type=\"button\"\n              className=\"p-2.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center ml-4\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Mobile Page content */}\n          <main className=\"py-6 px-4 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProtectedLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBA,MAAM,kBAAkD;QAAC,EAAE,QAAQ,EAAE;;IACnE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;KACD;IAED,MAAM,iBAAiB;QACrB,YAAY,CAAC;IACf;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAkE,OAAvB,WAAW,SAAS;;0BAE9E,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,eAAe;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;0DAEpE,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;;;;;;;;;;;;8CAOpE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sEAEpC,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAtBhB,KAAK,IAAI;;;;;4CA2BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,SAAS;wBACT,SAAS;4BACP,GAAG,cAAc,IAAI,CAAC;wBACxB;wBACA,YAAY;4BAAE,MAAM;4BAAU,SAAS;4BAAI,WAAW;wBAAI;wBAC1D,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAkD;;;;;;;;;;;8DAEpE,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;;;;;;;sDAIlE,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS;wDACP,OAAO,IAAI,CAAC,KAAK,IAAI;wDACrB,eAAe;oDACjB;;sEAEA,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAzBhB,KAAK,IAAI;;;;;4CA8BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtWM;;QACW,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFxB;uCAwWS", "debugId": null}}]}