{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/services/httpClient.ts"], "sourcesContent": ["import { store } from '@/lib/redux/store';\n\ninterface RequestOptions extends RequestInit {\n  params?: Record<string, string | number | boolean | undefined>;\n  requiresAuth?: boolean;\n}\n\nclass HttpClient {\n  private baseURL: string;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n  }\n\n  private getAuthHeaders(): HeadersInit {\n    const state = store.getState();\n    const token = state.auth?.token;\n    \n    return token ? { 'Authorization': `Bearer ${token}` } : {};\n  }\n\n  private buildUrl(endpoint: string, params?: Record<string, string | number | boolean | undefined>): string {\n    const url = new URL(`${this.baseURL}${endpoint}`);\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          url.searchParams.append(key, String(value));\n        }\n      });\n    }\n    \n    return url.toString();\n  }\n\n  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {\n    const { params, requiresAuth = true, ...fetchOptions } = options;\n    \n    const url = this.buildUrl(endpoint, params);\n    \n    const headers = new Headers({\n      'Content-Type': 'application/json',\n      ...fetchOptions.headers,\n      ...(requiresAuth ? this.getAuthHeaders() : {})\n    });\n\n    const response = await fetch(url, {\n      ...fetchOptions,\n      headers\n    });\n\n    // Handle 401 Unauthorized - could trigger logout or token refresh\n    if (response.status === 401) {\n      console.error('Authentication error: Unauthorized');\n      // Could dispatch a logout action here\n      // store.dispatch(logout());\n    }\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.detail || `Request failed with status ${response.status}`);\n    }\n\n    // For 204 No Content\n    if (response.status === 204) {\n      return {} as T;\n    }\n\n    return await response.json();\n  }\n\n  async get<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, { ...options, method: 'GET' });\n  }\n\n  async post<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  async patch<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  async delete<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {\n    return this.request<T>(endpoint, { ...options, method: 'DELETE' });\n  }\n\n  // Method for file uploads\n  async uploadFile<T>(endpoint: string, file: File, options: RequestOptions = {}): Promise<T> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'POST',\n      body: formData,\n      headers: {} // Let the browser set the content type with boundary\n    });\n  }\n\n  // Method for multiple file uploads\n  async uploadFiles<T>(endpoint: string, files: File[], options: RequestOptions = {}): Promise<T> {\n    const formData = new FormData();\n    files.forEach((file, index) => {\n      formData.append(`file${index}`, file);\n    });\n    \n    return this.request<T>(endpoint, {\n      ...options,\n      method: 'POST',\n      body: formData,\n      headers: {} // Let the browser set the content type with boundary\n    });\n  }\n}\n\n// Create and export a singleton instance\nexport const httpClient = new HttpClient(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1');\n\nexport default HttpClient;\n"], "names": [], "mappings": ";;;;AAqIyC;;AArIzC;;;AAOA,MAAM;IAOI,iBAA8B;YAEtB;QADd,MAAM,QAAQ,+HAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,MAAM,SAAQ,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,KAAK;QAE/B,OAAO,QAAQ;YAAE,iBAAiB,AAAC,UAAe,OAAN;QAAQ,IAAI,CAAC;IAC3D;IAEQ,SAAS,QAAgB,EAAE,MAA8D,EAAU;QACzG,MAAM,MAAM,IAAI,IAAI,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAEtC,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO;gBACtC;YACF;QACF;QAEA,OAAO,IAAI,QAAQ;IACrB;IAEA,MAAM,QAAW,QAAgB,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC5D,MAAM,EAAE,MAAM,EAAE,eAAe,IAAI,EAAE,GAAG,cAAc,GAAG;QAEzD,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU;QAEpC,MAAM,UAAU,IAAI,QAAQ;YAC1B,gBAAgB;YAChB,GAAG,aAAa,OAAO;YACvB,GAAI,eAAe,IAAI,CAAC,cAAc,KAAK,CAAC,CAAC;QAC/C;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,YAAY;YACf;QACF;QAEA,kEAAkE;QAClE,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,KAAK,CAAC;QACd,sCAAsC;QACtC,4BAA4B;QAC9B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,AAAC,8BAA6C,OAAhB,SAAS,MAAM;QACnF;QAEA,qBAAqB;QACrB,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,OAAO,CAAC;QACV;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B;IAEA,MAAM,IAAO,QAAgB,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACxD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAC/D;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACrE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACpE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,MAAS,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QACtE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,MAAM,OAAU,QAAgB,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IAClE;IAEA,0BAA0B;IAC1B,MAAM,WAAc,QAAgB,EAAE,IAAU,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC3E,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM;YACN,SAAS,CAAC,EAAE,qDAAqD;QACnE;IACF;IAEA,mCAAmC;IACnC,MAAM,YAAe,QAAgB,EAAE,KAAa,EAA4C;YAA1C,UAAA,iEAA0B,CAAC;QAC/E,MAAM,WAAW,IAAI;QACrB,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,SAAS,MAAM,CAAC,AAAC,OAAY,OAAN,QAAS;QAClC;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM;YACN,SAAS,CAAC,EAAE,qDAAqD;QACnE;IACF;IAvHA,YAAY,OAAe,CAAE;QAF7B,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG;IACjB;AAsHF;AAGO,MAAM,aAAa,IAAI,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;uCAE7D", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { apiService } from '@/lib/services/apiService';\n\ninterface User {\n  id: string;\n  username: string;\n  email?: string;\n}\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n};\n\n// Async thunks\nexport const loginUser = createAsyncThunk(\n  'auth/login',\n  async (credentials: { username: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await apiService.login(credentials);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Login failed');\n    }\n  }\n);\n\nexport const logoutUser = createAsyncThunk(\n  'auth/logout',\n  async (_, { rejectWithValue }) => {\n    try {\n      await apiService.logout();\n      return null;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Logout failed');\n    }\n  }\n);\n\nexport const getCurrentUser = createAsyncThunk(\n  'auth/getCurrentUser',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getCurrentUser();\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to get user');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setToken: (state, action: PayloadAction<string>) => {\n      state.token = action.payload;\n      state.isAuthenticated = true;\n    },\n    clearAuth: (state) => {\n      state.user = null;\n      state.token = null;\n      state.isAuthenticated = false;\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Login\n      .addCase(loginUser.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(loginUser.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.isAuthenticated = true;\n        state.error = null;\n      })\n      .addCase(loginUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n        state.isAuthenticated = false;\n      })\n      // Logout\n      .addCase(logoutUser.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(logoutUser.fulfilled, (state) => {\n        state.isLoading = false;\n        state.user = null;\n        state.token = null;\n        state.isAuthenticated = false;\n        state.error = null;\n      })\n      .addCase(logoutUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Get current user\n      .addCase(getCurrentUser.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(getCurrentUser.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload;\n        state.isAuthenticated = true;\n        state.error = null;\n      })\n      .addCase(getCurrentUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n        state.isAuthenticated = false;\n      });\n  },\n});\n\nexport const { clearError, setToken, clearAuth } = authSlice.actions;\nexport default authSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAgBA,MAAM,eAA0B;IAC9B,MAAM;IACN,OAAO;IACP,iBAAiB;IACjB,WAAW;IACX,OAAO;AACT;AAGO,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACtC,cACA,OAAO;QAAqD,EAAE,eAAe,EAAE;IAC7E,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,KAAK,CAAC;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,aAAa,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACvC,eACA,OAAO;QAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,uIAAA,CAAA,aAAU,CAAC,MAAM;QACvB,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC3C,uBACA,OAAO;QAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,cAAc;QAChD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGF,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;QAC1B;QACA,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,KAAK,GAAG;YACd,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB;IACF;IACA,eAAe,CAAC;QACd,OACE,QAAQ;SACP,OAAO,CAAC,UAAU,OAAO,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,UAAU,SAAS,EAAE,CAAC,OAAO;YACpC,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;YAChC,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YAClC,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,UAAU,QAAQ,EAAE,CAAC,OAAO;YACnC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;QAC1B,EACA,SAAS;SACR,OAAO,CAAC,WAAW,OAAO,EAAE,CAAC;YAC5B,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,WAAW,SAAS,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG;YACb,MAAM,KAAK,GAAG;YACd,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,WAAW,QAAQ,EAAE,CAAC,OAAO;YACpC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,mBAAmB;SAClB,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,eAAe,GAAG;QAC1B;IACJ;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,UAAU,OAAO;uCACrD,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/documentsSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { apiService } from '@/lib/services/apiService';\n\ninterface Document {\n  id: string;\n  title: string;\n  filename: string;\n  type: string;\n  size: string;\n  uploadedAt: string;\n  uploadedBy: string;\n  pages: number;\n  status: 'processing' | 'processed' | 'error';\n  presignedUrl?: string;\n}\n\ninterface DocumentsState {\n  documents: Document[];\n  selectedDocument: Document | null;\n  isLoading: boolean;\n  isUploading: boolean;\n  uploadProgress: number;\n  error: string | null;\n  searchQuery: string;\n  filters: {\n    type: string;\n    status: string;\n  };\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n  };\n}\n\nconst initialState: DocumentsState = {\n  documents: [],\n  selectedDocument: null,\n  isLoading: false,\n  isUploading: false,\n  uploadProgress: 0,\n  error: null,\n  searchQuery: '',\n  filters: {\n    type: 'all',\n    status: 'all',\n  },\n  pagination: {\n    page: 1,\n    limit: 20,\n    total: 0,\n  },\n};\n\n// Async thunks\nexport const fetchDocuments = createAsyncThunk(\n  'documents/fetchDocuments',\n  async (params: { page?: number; limit?: number; search?: string }, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getDocuments(params);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch documents');\n    }\n  }\n);\n\nexport const uploadDocuments = createAsyncThunk(\n  'documents/uploadDocuments',\n  async (files: File[], { rejectWithValue, dispatch }) => {\n    try {\n      const response = await apiService.uploadDocuments(files, (progress) => {\n        dispatch(setUploadProgress(progress));\n      });\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Upload failed');\n    }\n  }\n);\n\nexport const deleteDocument = createAsyncThunk(\n  'documents/deleteDocument',\n  async (documentId: string, { rejectWithValue }) => {\n    try {\n      await apiService.deleteDocument(documentId);\n      return documentId;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to delete document');\n    }\n  }\n);\n\nexport const getDocumentDetails = createAsyncThunk(\n  'documents/getDocumentDetails',\n  async (documentId: string, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getDocument(documentId);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to get document details');\n    }\n  }\n);\n\nconst documentsSlice = createSlice({\n  name: 'documents',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setSearchQuery: (state, action: PayloadAction<string>) => {\n      state.searchQuery = action.payload;\n    },\n    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    setUploadProgress: (state, action: PayloadAction<number>) => {\n      state.uploadProgress = action.payload;\n    },\n    resetUpload: (state) => {\n      state.isUploading = false;\n      state.uploadProgress = 0;\n    },\n    setSelectedDocument: (state, action: PayloadAction<Document | null>) => {\n      state.selectedDocument = action.payload;\n    },\n    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch documents\n      .addCase(fetchDocuments.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchDocuments.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.documents = action.payload.documents;\n        state.pagination.total = action.payload.total;\n        state.error = null;\n      })\n      .addCase(fetchDocuments.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Upload documents\n      .addCase(uploadDocuments.pending, (state) => {\n        state.isUploading = true;\n        state.uploadProgress = 0;\n        state.error = null;\n      })\n      .addCase(uploadDocuments.fulfilled, (state, action) => {\n        state.isUploading = false;\n        state.uploadProgress = 100;\n        // Add new documents to the list\n        state.documents = [...action.payload.documents, ...state.documents];\n        state.error = null;\n      })\n      .addCase(uploadDocuments.rejected, (state, action) => {\n        state.isUploading = false;\n        state.uploadProgress = 0;\n        state.error = action.payload as string;\n      })\n      // Delete document\n      .addCase(deleteDocument.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(deleteDocument.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.documents = state.documents.filter(doc => doc.id !== action.payload);\n        if (state.selectedDocument?.id === action.payload) {\n          state.selectedDocument = null;\n        }\n      })\n      .addCase(deleteDocument.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Get document details\n      .addCase(getDocumentDetails.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(getDocumentDetails.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.selectedDocument = action.payload;\n      })\n      .addCase(getDocumentDetails.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const {\n  clearError,\n  setSearchQuery,\n  setFilters,\n  setUploadProgress,\n  resetUpload,\n  setSelectedDocument,\n  setPagination,\n} = documentsSlice.actions;\n\nexport default documentsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAkCA,MAAM,eAA+B;IACnC,WAAW,EAAE;IACb,kBAAkB;IAClB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,SAAS;QACP,MAAM;QACN,QAAQ;IACV;IACA,YAAY;QACV,MAAM;QACN,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC3C,4BACA,OAAO;QAA4D,EAAE,eAAe,EAAE;IACpF,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,YAAY,CAAC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,kBAAkB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC5C,6BACA,OAAO;QAAe,EAAE,eAAe,EAAE,QAAQ,EAAE;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,eAAe,CAAC,OAAO,CAAC;YACxD,SAAS,kBAAkB;QAC7B;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC3C,4BACA,OAAO;QAAoB,EAAE,eAAe,EAAE;IAC5C,IAAI;QACF,MAAM,uIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC/C,gCACA,OAAO;QAAoB,EAAE,eAAe,EAAE;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,WAAW,CAAC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGF,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACjC,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG;gBAAE,GAAG,MAAM,OAAO;gBAAE,GAAG,OAAO,OAAO;YAAC;QACxD;QACA,mBAAmB,CAAC,OAAO;YACzB,MAAM,cAAc,GAAG,OAAO,OAAO;QACvC;QACA,aAAa,CAAC;YACZ,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;QACzB;QACA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,gBAAgB,GAAG,OAAO,OAAO;QACzC;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG;gBAAE,GAAG,MAAM,UAAU;gBAAE,GAAG,OAAO,OAAO;YAAC;QAC9D;IACF;IACA,eAAe,CAAC;QACd,OACE,kBAAkB;SACjB,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,SAAS,GAAG;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS;YAC1C,MAAM,UAAU,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK;YAC7C,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,mBAAmB;SAClB,OAAO,CAAC,gBAAgB,OAAO,EAAE,CAAC;YACjC,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;YACvB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,gBAAgB,SAAS,EAAE,CAAC,OAAO;YAC1C,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;YACvB,gCAAgC;YAChC,MAAM,SAAS,GAAG;mBAAI,OAAO,OAAO,CAAC,SAAS;mBAAK,MAAM,SAAS;aAAC;YACnE,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,gBAAgB,QAAQ,EAAE,CAAC,OAAO;YACzC,MAAM,WAAW,GAAG;YACpB,MAAM,cAAc,GAAG;YACvB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,kBAAkB;SACjB,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;gBAGrC;YAFJ,MAAM,SAAS,GAAG;YAClB,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO;YACzE,IAAI,EAAA,0BAAA,MAAM,gBAAgB,cAAtB,8CAAA,wBAAwB,EAAE,MAAK,OAAO,OAAO,EAAE;gBACjD,MAAM,gBAAgB,GAAG;YAC3B;QACF,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,uBAAuB;SACtB,OAAO,CAAC,mBAAmB,OAAO,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,mBAAmB,SAAS,EAAE,CAAC,OAAO;YAC7C,MAAM,SAAS,GAAG;YAClB,MAAM,gBAAgB,GAAG,OAAO,OAAO;QACzC,GACC,OAAO,CAAC,mBAAmB,QAAQ,EAAE,CAAC,OAAO;YAC5C,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACV,iBAAiB,EACjB,WAAW,EACX,mBAAmB,EACnB,aAAa,EACd,GAAG,eAAe,OAAO;uCAEX,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/chatSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { apiService } from '@/lib/services/apiService';\n\ninterface Source {\n  documentId: string;\n  filename: string;\n  pageNumber: number;\n  snippet: string;\n  confidenceScore: number;\n}\n\ninterface Message {\n  id: string;\n  type: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  sources?: Source[];\n  conversationId?: string;\n}\n\ninterface Conversation {\n  id: string;\n  title: string;\n  createdAt: Date;\n  updatedAt: Date;\n  messageCount: number;\n}\n\ninterface ChatState {\n  conversations: Conversation[];\n  currentConversation: string | null;\n  messages: Message[];\n  isLoading: boolean;\n  isTyping: boolean;\n  error: string | null;\n  searchResults: any[];\n}\n\nconst initialState: ChatState = {\n  conversations: [],\n  currentConversation: null,\n  messages: [],\n  isLoading: false,\n  isTyping: false,\n  error: null,\n  searchResults: [],\n};\n\n// Async thunks\nexport const sendMessage = createAsyncThunk(\n  'chat/sendMessage',\n  async (\n    params: { \n      message: string; \n      conversationId?: string; \n      maxResults?: number \n    }, \n    { rejectWithValue }\n  ) => {\n    try {\n      const response = await apiService.sendChatMessage(params);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to send message');\n    }\n  }\n);\n\nexport const fetchConversations = createAsyncThunk(\n  'chat/fetchConversations',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getChatConversations();\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch conversations');\n    }\n  }\n);\n\nexport const fetchChatHistory = createAsyncThunk(\n  'chat/fetchChatHistory',\n  async (conversationId: string, { rejectWithValue }) => {\n    try {\n      const response = await apiService.getChatHistory(conversationId);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch chat history');\n    }\n  }\n);\n\nexport const deleteConversation = createAsyncThunk(\n  'chat/deleteConversation',\n  async (conversationId: string, { rejectWithValue }) => {\n    try {\n      await apiService.deleteConversation(conversationId);\n      return conversationId;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to delete conversation');\n    }\n  }\n);\n\nexport const searchInChat = createAsyncThunk(\n  'chat/searchInChat',\n  async (query: string, { rejectWithValue }) => {\n    try {\n      const response = await apiService.searchInChat(query);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Search failed');\n    }\n  }\n);\n\nconst chatSlice = createSlice({\n  name: 'chat',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setCurrentConversation: (state, action: PayloadAction<string | null>) => {\n      state.currentConversation = action.payload;\n    },\n    addUserMessage: (state, action: PayloadAction<Message>) => {\n      state.messages.push(action.payload);\n    },\n    setTyping: (state, action: PayloadAction<boolean>) => {\n      state.isTyping = action.payload;\n    },\n    clearMessages: (state) => {\n      state.messages = [];\n      state.currentConversation = null;\n    },\n    clearSearchResults: (state) => {\n      state.searchResults = [];\n    },\n    updateMessage: (state, action: PayloadAction<{ id: string; updates: Partial<Message> }>) => {\n      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload.id);\n      if (messageIndex !== -1) {\n        state.messages[messageIndex] = { ...state.messages[messageIndex], ...action.payload.updates };\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Send message\n      .addCase(sendMessage.pending, (state) => {\n        state.isTyping = true;\n        state.error = null;\n      })\n      .addCase(sendMessage.fulfilled, (state, action) => {\n        state.isTyping = false;\n        state.messages.push({\n          id: Date.now().toString(),\n          type: 'assistant',\n          content: action.payload.answer,\n          timestamp: new Date(),\n          sources: action.payload.sources,\n          conversationId: action.payload.conversationId,\n        });\n        state.currentConversation = action.payload.conversationId;\n        state.error = null;\n      })\n      .addCase(sendMessage.rejected, (state, action) => {\n        state.isTyping = false;\n        state.error = action.payload as string;\n      })\n      // Fetch conversations\n      .addCase(fetchConversations.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(fetchConversations.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.conversations = action.payload;\n      })\n      .addCase(fetchConversations.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch chat history\n      .addCase(fetchChatHistory.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(fetchChatHistory.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.messages = action.payload.map((chat: any) => [\n          {\n            id: `${chat.id}-user`,\n            type: 'user' as const,\n            content: chat.query,\n            timestamp: new Date(chat.timestamp),\n            conversationId: chat.conversationId,\n          },\n          {\n            id: `${chat.id}-assistant`,\n            type: 'assistant' as const,\n            content: chat.response,\n            timestamp: new Date(chat.timestamp),\n            sources: chat.sources,\n            conversationId: chat.conversationId,\n          },\n        ]).flat();\n      })\n      .addCase(fetchChatHistory.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Delete conversation\n      .addCase(deleteConversation.fulfilled, (state, action) => {\n        state.conversations = state.conversations.filter(conv => conv.id !== action.payload);\n        if (state.currentConversation === action.payload) {\n          state.currentConversation = null;\n          state.messages = [];\n        }\n      })\n      // Search in chat\n      .addCase(searchInChat.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(searchInChat.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.searchResults = action.payload;\n      })\n      .addCase(searchInChat.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const {\n  clearError,\n  setCurrentConversation,\n  addUserMessage,\n  setTyping,\n  clearMessages,\n  clearSearchResults,\n  updateMessage,\n} = chatSlice.actions;\n\nexport default chatSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAqCA,MAAM,eAA0B;IAC9B,eAAe,EAAE;IACjB,qBAAqB;IACrB,UAAU,EAAE;IACZ,WAAW;IACX,UAAU;IACV,OAAO;IACP,eAAe,EAAE;AACnB;AAGO,MAAM,cAAc,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACxC,oBACA,OACE;QAKA,EAAE,eAAe,EAAE;IAEnB,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;QAClD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC/C,2BACA,OAAO;QAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,oBAAoB;QACtD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,mBAAmB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC7C,yBACA,OAAO;QAAwB,EAAE,eAAe,EAAE;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QACjD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAC/C,2BACA,OAAO;QAAwB,EAAE,eAAe,EAAE;IAChD,IAAI;QACF,MAAM,uIAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGK,MAAM,eAAe,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EACzC,qBACA,OAAO;QAAe,EAAE,eAAe,EAAE;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,aAAU,CAAC,YAAY,CAAC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gBAAgB,MAAM,OAAO,IAAI;IAC1C;AACF;AAGF,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QACA,wBAAwB,CAAC,OAAO;YAC9B,MAAM,mBAAmB,GAAG,OAAO,OAAO;QAC5C;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,OAAO;QACpC;QACA,WAAW,CAAC,OAAO;YACjB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,eAAe,CAAC;YACd,MAAM,QAAQ,GAAG,EAAE;YACnB,MAAM,mBAAmB,GAAG;QAC9B;QACA,oBAAoB,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE;QAC1B;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,eAAe,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;YACjF,IAAI,iBAAiB,CAAC,GAAG;gBACvB,MAAM,QAAQ,CAAC,aAAa,GAAG;oBAAE,GAAG,MAAM,QAAQ,CAAC,aAAa;oBAAE,GAAG,OAAO,OAAO,CAAC,OAAO;gBAAC;YAC9F;QACF;IACF;IACA,eAAe,CAAC;QACd,OACE,eAAe;SACd,OAAO,CAAC,YAAY,OAAO,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,SAAS,EAAE,CAAC,OAAO;YACtC,MAAM,QAAQ,GAAG;YACjB,MAAM,QAAQ,CAAC,IAAI,CAAC;gBAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,SAAS,OAAO,OAAO,CAAC,MAAM;gBAC9B,WAAW,IAAI;gBACf,SAAS,OAAO,OAAO,CAAC,OAAO;gBAC/B,gBAAgB,OAAO,OAAO,CAAC,cAAc;YAC/C;YACA,MAAM,mBAAmB,GAAG,OAAO,OAAO,CAAC,cAAc;YACzD,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC,OAAO;YACrC,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,sBAAsB;SACrB,OAAO,CAAC,mBAAmB,OAAO,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,mBAAmB,SAAS,EAAE,CAAC,OAAO;YAC7C,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC,GACC,OAAO,CAAC,mBAAmB,QAAQ,EAAE,CAAC,OAAO;YAC5C,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,qBAAqB;SACpB,OAAO,CAAC,iBAAiB,OAAO,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,iBAAiB,SAAS,EAAE,CAAC,OAAO;YAC3C,MAAM,SAAS,GAAG;YAClB,MAAM,QAAQ,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAc;oBACjD;wBACE,IAAI,AAAC,GAAU,OAAR,KAAK,EAAE,EAAC;wBACf,MAAM;wBACN,SAAS,KAAK,KAAK;wBACnB,WAAW,IAAI,KAAK,KAAK,SAAS;wBAClC,gBAAgB,KAAK,cAAc;oBACrC;oBACA;wBACE,IAAI,AAAC,GAAU,OAAR,KAAK,EAAE,EAAC;wBACf,MAAM;wBACN,SAAS,KAAK,QAAQ;wBACtB,WAAW,IAAI,KAAK,KAAK,SAAS;wBAClC,SAAS,KAAK,OAAO;wBACrB,gBAAgB,KAAK,cAAc;oBACrC;iBACD,EAAE,IAAI;QACT,GACC,OAAO,CAAC,iBAAiB,QAAQ,EAAE,CAAC,OAAO;YAC1C,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EACA,sBAAsB;SACrB,OAAO,CAAC,mBAAmB,SAAS,EAAE,CAAC,OAAO;YAC7C,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;YACnF,IAAI,MAAM,mBAAmB,KAAK,OAAO,OAAO,EAAE;gBAChD,MAAM,mBAAmB,GAAG;gBAC5B,MAAM,QAAQ,GAAG,EAAE;YACrB;QACF,EACA,iBAAiB;SAChB,OAAO,CAAC,aAAa,OAAO,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG;QACpB,GACC,OAAO,CAAC,aAAa,SAAS,EAAE,CAAC,OAAO;YACvC,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC,GACC,OAAO,CAAC,aAAa,QAAQ,EAAE,CAAC,OAAO;YACtC,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EACX,UAAU,EACV,sBAAsB,EACtB,cAAc,EACd,SAAS,EACT,aAAa,EACb,kBAAkB,EAClB,aAAa,EACd,GAAG,UAAU,OAAO;uCAEN,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\ninterface Toast {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  message: string;\n  duration?: number;\n}\n\ninterface Modal {\n  id: string;\n  type: string;\n  props?: any;\n}\n\ninterface UIState {\n  theme: 'light' | 'dark';\n  sidebarOpen: boolean;\n  toasts: Toast[];\n  modals: Modal[];\n  isLoading: boolean;\n  loadingMessage: string;\n  viewMode: 'grid' | 'list';\n}\n\nconst initialState: UIState = {\n  theme: 'light',\n  sidebarOpen: false,\n  toasts: [],\n  modals: [],\n  isLoading: false,\n  loadingMessage: '',\n  viewMode: 'grid',\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n    },\n    toggleTheme: (state) => {\n      state.theme = state.theme === 'light' ? 'dark' : 'light';\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    addToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {\n      const toast: Toast = {\n        id: Date.now().toString(),\n        duration: 5000,\n        ...action.payload,\n      };\n      state.toasts.push(toast);\n    },\n    removeToast: (state, action: PayloadAction<string>) => {\n      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);\n    },\n    clearToasts: (state) => {\n      state.toasts = [];\n    },\n    openModal: (state, action: PayloadAction<Omit<Modal, 'id'>>) => {\n      const modal: Modal = {\n        id: Date.now().toString(),\n        ...action.payload,\n      };\n      state.modals.push(modal);\n    },\n    closeModal: (state, action: PayloadAction<string>) => {\n      state.modals = state.modals.filter(modal => modal.id !== action.payload);\n    },\n    closeAllModals: (state) => {\n      state.modals = [];\n    },\n    setLoading: (state, action: PayloadAction<{ isLoading: boolean; message?: string }>) => {\n      state.isLoading = action.payload.isLoading;\n      state.loadingMessage = action.payload.message || '';\n    },\n    setViewMode: (state, action: PayloadAction<'grid' | 'list'>) => {\n      state.viewMode = action.payload;\n    },\n  },\n});\n\nexport const {\n  setTheme,\n  toggleTheme,\n  setSidebarOpen,\n  toggleSidebar,\n  addToast,\n  removeToast,\n  clearToasts,\n  openModal,\n  closeModal,\n  closeAllModals,\n  setLoading,\n  setViewMode,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAyBA,MAAM,eAAwB;IAC5B,OAAO;IACP,aAAa;IACb,QAAQ,EAAE;IACV,QAAQ,EAAE;IACV,WAAW;IACX,gBAAgB;IAChB,UAAU;AACZ;AAEA,MAAM,UAAU,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACR,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QACA,aAAa,CAAC;YACZ,MAAM,KAAK,GAAG,MAAM,KAAK,KAAK,UAAU,SAAS;QACnD;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,eAAe,CAAC;YACd,MAAM,WAAW,GAAG,CAAC,MAAM,WAAW;QACxC;QACA,UAAU,CAAC,OAAO;YAChB,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,UAAU;gBACV,GAAG,OAAO,OAAO;YACnB;YACA,MAAM,MAAM,CAAC,IAAI,CAAC;QACpB;QACA,aAAa,CAAC,OAAO;YACnB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO,OAAO;QACzE;QACA,aAAa,CAAC;YACZ,MAAM,MAAM,GAAG,EAAE;QACnB;QACA,WAAW,CAAC,OAAO;YACjB,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,OAAO,OAAO;YACnB;YACA,MAAM,MAAM,CAAC,IAAI,CAAC;QACpB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO,OAAO;QACzE;QACA,gBAAgB,CAAC;YACf,MAAM,MAAM,GAAG,EAAE;QACnB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS;YAC1C,MAAM,cAAc,GAAG,OAAO,OAAO,CAAC,OAAO,IAAI;QACnD;QACA,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;IACF;AACF;AAEO,MAAM,EACX,QAAQ,EACR,WAAW,EACX,cAAc,EACd,aAAa,EACb,QAAQ,EACR,WAAW,EACX,WAAW,EACX,SAAS,EACT,UAAU,EACV,cAAc,EACd,UAAU,EACV,WAAW,EACZ,GAAG,QAAQ,OAAO;uCAEJ,QAAQ,OAAO", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/store.ts"], "sourcesContent": ["import { configureStore, combineReducers } from '@reduxjs/toolkit';\nimport { \n  persistStore, \n  persistReducer,\n  FLUSH,\n  REHYDRATE,\n  PAUSE,\n  PERSIST,\n  PURGE,\n  REGISTER\n} from 'redux-persist';\nimport storage from 'redux-persist/lib/storage';\nimport authReducer from './slices/authSlice';\nimport documentsReducer from './slices/documentsSlice';\nimport chatReducer from './slices/chatSlice';\nimport uiReducer from './slices/uiSlice';\n\nconst persistConfig = {\n  key: 'root',\n  storage,\n  whitelist: ['auth'], // only auth will be persisted\n};\n\nconst rootReducer = combineReducers({\n  auth: authReducer,\n  documents: documentsReducer,\n  chat: chatReducer,\n  ui: uiReducer,\n});\n\nconst persistedReducer = persistReducer(persistConfig, rootReducer);\n\nexport const store = configureStore({\n  reducer: persistedReducer,\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\n      },\n    }),\n});\n\nexport const persistor = persistStore(store);\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,gBAAgB;IACpB,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;IACP,WAAW;QAAC;KAAO;AACrB;AAEA,MAAM,cAAc,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,6IAAA,CAAA,UAAW;IACjB,WAAW,kJAAA,CAAA,UAAgB;IAC3B,MAAM,6IAAA,CAAA,UAAW;IACjB,IAAI,2IAAA,CAAA,UAAS;AACf;AAEA,MAAM,mBAAmB,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;AAEhD,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;IACT,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,YAAS;oBAAE,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,UAAO;oBAAE,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,WAAQ;iBAAC;YACrE;QACF;AACJ;AAEO,MAAM,YAAY,CAAA,GAAA,oMAAA,CAAA,eAAY,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/lib/redux/provider.tsx"], "sourcesContent": ["'use client';\n\nimport { Provider } from 'react-redux';\nimport { PersistGate } from 'redux-persist/integration/react';\nimport { store, persistor } from './store';\n\nexport function ReduxProvider({ children }: { children: React.ReactNode }) {\n  return (\n    <Provider store={store}>\n      <PersistGate loading={null} persistor={persistor}>\n        {children}\n      </PersistGate>\n    </Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;IAC5B,qBACE,6LAAC,4JAAA,CAAA,WAAQ;QAAC,OAAO,+HAAA,CAAA,QAAK;kBACpB,cAAA,6LAAC,iKAAA,CAAA,cAAW;YAAC,SAAS;YAAM,WAAW,+HAAA,CAAA,YAAS;sBAC7C;;;;;;;;;;;AAIT;KARgB", "debugId": null}}]}