(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/services/apiService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiService": ()=>apiService
});
class ApiService {
    async request(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        const url = "".concat(this.baseURL).concat(endpoint);
        const config = {
            headers: this.getHeaders(),
            ...options
        };
        try {
            const response = await fetch(url, config);
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({}));
                throw new Error(errorData.detail || "HTTP ".concat(response.status, ": ").concat(response.statusText));
            }
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            return await response.text();
        } catch (error) {
            console.error("API request failed: ".concat(endpoint), error);
            throw error;
        }
    }
    async uploadRequest(endpoint, formData, onProgress) {
        const url = "".concat(this.baseURL).concat(endpoint);
        return new Promise((resolve, reject)=>{
            const xhr = new XMLHttpRequest();
            if (onProgress) {
                xhr.upload.addEventListener('progress', (e)=>{
                    if (e.lengthComputable) {
                        const percentComplete = e.loaded / e.total * 100;
                        onProgress(percentComplete);
                    }
                });
            }
            xhr.addEventListener('load', ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Invalid JSON response'));
                    }
                } else {
                    try {
                        const errorData = JSON.parse(xhr.responseText);
                        reject(new Error(errorData.detail || "HTTP ".concat(xhr.status, ": ").concat(xhr.statusText)));
                    } catch (error) {
                        reject(new Error("HTTP ".concat(xhr.status, ": ").concat(xhr.statusText)));
                    }
                }
            });
            xhr.addEventListener('error', ()=>{
                reject(new Error('Network error occurred'));
            });
            xhr.open('POST', url);
            // Add authorization header
            if (this.token) {
                xhr.setRequestHeader('Authorization', "Bearer ".concat(this.token));
            }
            xhr.send(formData);
        });
    }
    setToken(token) {
        this.token = token;
        if ("TURBOPACK compile-time truthy", 1) {
            if (token) {
                localStorage.setItem('authToken', token);
            } else {
                localStorage.removeItem('authToken');
            }
        }
    }
    // Authentication
    async login(credentials) {
        const response = await this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
        this.setToken(response.token);
        return response;
    }
    async logout() {
        try {
            await this.request('/auth/logout', {
                method: 'POST'
            });
        } finally{
            this.setToken(null);
        }
    }
    async getCurrentUser() {
        return this.request('/auth/me');
    }
    // Documents
    async getDocuments() {
        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        const searchParams = new URLSearchParams();
        if (params.page) searchParams.append('skip', ((params.page - 1) * (params.limit || 20)).toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.search) searchParams.append('search', params.search);
        return this.request("/knowledge-base/documents?".concat(searchParams));
    }
    async getDocument(documentId) {
        return this.request("/knowledge-base/documents/".concat(documentId));
    }
    async uploadDocuments(files, onProgress) {
        const formData = new FormData();
        files.forEach((file)=>{
            formData.append('files', file);
        });
        return this.uploadRequest('/knowledge-base/upload', formData, onProgress);
    }
    async deleteDocument(documentId) {
        return this.request("/knowledge-base/documents/".concat(documentId), {
            method: 'DELETE'
        });
    }
    async getDocumentPages(documentId) {
        return this.request("/knowledge-base/documents/".concat(documentId, "/pages"));
    }
    async searchDocuments(query) {
        let maxResults = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;
        return this.request("/knowledge-base/search?query=".concat(encodeURIComponent(query), "&max_results=").concat(maxResults));
    }
    async searchAllDocuments() {
        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        const searchParams = new URLSearchParams();
        if (params.query) searchParams.append('query', params.query);
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.page_size) searchParams.append('page_size', params.page_size.toString());
        if (params.offset) searchParams.append('offset', params.offset);
        if (params.include_vectors) searchParams.append('include_vectors', params.include_vectors.toString());
        if (params.filter_by_document) searchParams.append('filter_by_document', params.filter_by_document);
        if (params.filter_by_type) searchParams.append('filter_by_type', params.filter_by_type);
        return this.request("/knowledge-base/search_all?".concat(searchParams));
    }
    // Chat
    async sendChatMessage(params) {
        return this.request('/chat/query', {
            method: 'POST',
            body: JSON.stringify({
                query: params.message,
                max_results: params.maxResults || 5,
                conversation_id: params.conversationId,
                include_sources: true,
                language: 'nepali'
            })
        });
    }
    async getChatHistory(conversationId) {
        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50, skip = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
        let url = "/chat/history?limit=".concat(limit, "&skip=").concat(skip);
        if (conversationId) {
            url += "&conversation_id=".concat(conversationId);
        }
        return this.request(url);
    }
    async getChatConversations() {
        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;
        return this.request("/chat/conversations?limit=".concat(limit));
    }
    async deleteConversation(conversationId) {
        return this.request("/chat/conversation/".concat(conversationId), {
            method: 'DELETE'
        });
    }
    async searchInChat(query) {
        let maxResults = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;
        return this.request("/chat/search?query=".concat(encodeURIComponent(query), "&max_results=").concat(maxResults));
    }
    // Source Attribution
    async getSourceAttributionMap(documentId) {
        return this.request("/chat/attribution/".concat(documentId));
    }
    async getChunkLocation(chunkId) {
        return this.request("/chat/chunk/".concat(chunkId, "/location"));
    }
    // Health Check
    async healthCheck() {
        return this.request('/chat/health');
    }
    constructor(){
    // httpClient handles all the base configuration
    }
}
const apiService = new ApiService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/slices/authSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAuth": ()=>clearAuth,
    "clearError": ()=>clearError,
    "default": ()=>__TURBOPACK__default__export__,
    "getCurrentUser": ()=>getCurrentUser,
    "loginUser": ()=>loginUser,
    "logoutUser": ()=>logoutUser,
    "setToken": ()=>setToken
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript)");
;
;
const initialState = {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
};
const loginUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/login', async (credentials, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].login(credentials);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Login failed');
    }
});
const logoutUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/logout', async (_, param)=>{
    let { rejectWithValue } = param;
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].logout();
        return null;
    } catch (error) {
        return rejectWithValue(error.message || 'Logout failed');
    }
});
const getCurrentUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/getCurrentUser', async (_, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getCurrentUser();
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to get user');
    }
});
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'auth',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setToken: (state, action)=>{
            state.token = action.payload;
            state.isAuthenticated = true;
        },
        clearAuth: (state)=>{
            state.user = null;
            state.token = null;
            state.isAuthenticated = false;
            state.error = null;
        }
    },
    extraReducers: (builder)=>{
        builder// Login
        .addCase(loginUser.pending, (state)=>{
            state.isLoading = true;
            state.error = null;
        }).addCase(loginUser.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.user = action.payload.user;
            state.token = action.payload.token;
            state.isAuthenticated = true;
            state.error = null;
        }).addCase(loginUser.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
            state.isAuthenticated = false;
        })// Logout
        .addCase(logoutUser.pending, (state)=>{
            state.isLoading = true;
        }).addCase(logoutUser.fulfilled, (state)=>{
            state.isLoading = false;
            state.user = null;
            state.token = null;
            state.isAuthenticated = false;
            state.error = null;
        }).addCase(logoutUser.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Get current user
        .addCase(getCurrentUser.pending, (state)=>{
            state.isLoading = true;
        }).addCase(getCurrentUser.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.user = action.payload;
            state.isAuthenticated = true;
            state.error = null;
        }).addCase(getCurrentUser.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
            state.isAuthenticated = false;
        });
    }
});
const { clearError, setToken, clearAuth } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/slices/documentsSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearError": ()=>clearError,
    "default": ()=>__TURBOPACK__default__export__,
    "deleteDocument": ()=>deleteDocument,
    "fetchDocuments": ()=>fetchDocuments,
    "getDocumentDetails": ()=>getDocumentDetails,
    "resetUpload": ()=>resetUpload,
    "setFilters": ()=>setFilters,
    "setPagination": ()=>setPagination,
    "setSearchQuery": ()=>setSearchQuery,
    "setSelectedDocument": ()=>setSelectedDocument,
    "setUploadProgress": ()=>setUploadProgress,
    "uploadDocuments": ()=>uploadDocuments
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript)");
;
;
const initialState = {
    documents: [],
    selectedDocument: null,
    isLoading: false,
    isUploading: false,
    uploadProgress: 0,
    error: null,
    searchQuery: '',
    filters: {
        type: 'all',
        status: 'all'
    },
    pagination: {
        page: 1,
        limit: 20,
        total: 0
    }
};
const fetchDocuments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/fetchDocuments', async (params, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getDocuments(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to fetch documents');
    }
});
const uploadDocuments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/uploadDocuments', async (files, param)=>{
    let { rejectWithValue, dispatch } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].uploadDocuments(files, (progress)=>{
            dispatch(setUploadProgress(progress));
        });
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Upload failed');
    }
});
const deleteDocument = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/deleteDocument', async (documentId, param)=>{
    let { rejectWithValue } = param;
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].deleteDocument(documentId);
        return documentId;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to delete document');
    }
});
const getDocumentDetails = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/getDocumentDetails', async (documentId, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getDocument(documentId);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to get document details');
    }
});
const documentsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'documents',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setSearchQuery: (state, action)=>{
            state.searchQuery = action.payload;
        },
        setFilters: (state, action)=>{
            state.filters = {
                ...state.filters,
                ...action.payload
            };
        },
        setUploadProgress: (state, action)=>{
            state.uploadProgress = action.payload;
        },
        resetUpload: (state)=>{
            state.isUploading = false;
            state.uploadProgress = 0;
        },
        setSelectedDocument: (state, action)=>{
            state.selectedDocument = action.payload;
        },
        setPagination: (state, action)=>{
            state.pagination = {
                ...state.pagination,
                ...action.payload
            };
        }
    },
    extraReducers: (builder)=>{
        builder// Fetch documents
        .addCase(fetchDocuments.pending, (state)=>{
            state.isLoading = true;
            state.error = null;
        }).addCase(fetchDocuments.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.documents = action.payload.documents;
            state.pagination.total = action.payload.total;
            state.error = null;
        }).addCase(fetchDocuments.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Upload documents
        .addCase(uploadDocuments.pending, (state)=>{
            state.isUploading = true;
            state.uploadProgress = 0;
            state.error = null;
        }).addCase(uploadDocuments.fulfilled, (state, action)=>{
            state.isUploading = false;
            state.uploadProgress = 100;
            // Add new documents to the list
            state.documents = [
                ...action.payload.documents,
                ...state.documents
            ];
            state.error = null;
        }).addCase(uploadDocuments.rejected, (state, action)=>{
            state.isUploading = false;
            state.uploadProgress = 0;
            state.error = action.payload;
        })// Delete document
        .addCase(deleteDocument.pending, (state)=>{
            state.isLoading = true;
        }).addCase(deleteDocument.fulfilled, (state, action)=>{
            var _state_selectedDocument;
            state.isLoading = false;
            state.documents = state.documents.filter((doc)=>doc.id !== action.payload);
            if (((_state_selectedDocument = state.selectedDocument) === null || _state_selectedDocument === void 0 ? void 0 : _state_selectedDocument.id) === action.payload) {
                state.selectedDocument = null;
            }
        }).addCase(deleteDocument.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Get document details
        .addCase(getDocumentDetails.pending, (state)=>{
            state.isLoading = true;
        }).addCase(getDocumentDetails.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.selectedDocument = action.payload;
        }).addCase(getDocumentDetails.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        });
    }
});
const { clearError, setSearchQuery, setFilters, setUploadProgress, resetUpload, setSelectedDocument, setPagination } = documentsSlice.actions;
const __TURBOPACK__default__export__ = documentsSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/slices/chatSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addUserMessage": ()=>addUserMessage,
    "clearError": ()=>clearError,
    "clearMessages": ()=>clearMessages,
    "clearSearchResults": ()=>clearSearchResults,
    "default": ()=>__TURBOPACK__default__export__,
    "deleteConversation": ()=>deleteConversation,
    "fetchChatHistory": ()=>fetchChatHistory,
    "fetchConversations": ()=>fetchConversations,
    "searchInChat": ()=>searchInChat,
    "sendMessage": ()=>sendMessage,
    "setCurrentConversation": ()=>setCurrentConversation,
    "setTyping": ()=>setTyping,
    "updateMessage": ()=>updateMessage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript)");
;
;
const initialState = {
    conversations: [],
    currentConversation: null,
    messages: [],
    isLoading: false,
    isTyping: false,
    error: null,
    searchResults: []
};
const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/sendMessage', async (params, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].sendChatMessage(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to send message');
    }
});
const fetchConversations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/fetchConversations', async (_, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getChatConversations();
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to fetch conversations');
    }
});
const fetchChatHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/fetchChatHistory', async (conversationId, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getChatHistory(conversationId);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to fetch chat history');
    }
});
const deleteConversation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/deleteConversation', async (conversationId, param)=>{
    let { rejectWithValue } = param;
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].deleteConversation(conversationId);
        return conversationId;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to delete conversation');
    }
});
const searchInChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/searchInChat', async (query, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].searchInChat(query);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Search failed');
    }
});
const chatSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'chat',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setCurrentConversation: (state, action)=>{
            state.currentConversation = action.payload;
        },
        addUserMessage: (state, action)=>{
            state.messages.push(action.payload);
        },
        setTyping: (state, action)=>{
            state.isTyping = action.payload;
        },
        clearMessages: (state)=>{
            state.messages = [];
            state.currentConversation = null;
        },
        clearSearchResults: (state)=>{
            state.searchResults = [];
        },
        updateMessage: (state, action)=>{
            const messageIndex = state.messages.findIndex((msg)=>msg.id === action.payload.id);
            if (messageIndex !== -1) {
                state.messages[messageIndex] = {
                    ...state.messages[messageIndex],
                    ...action.payload.updates
                };
            }
        }
    },
    extraReducers: (builder)=>{
        builder// Send message
        .addCase(sendMessage.pending, (state)=>{
            state.isTyping = true;
            state.error = null;
        }).addCase(sendMessage.fulfilled, (state, action)=>{
            state.isTyping = false;
            state.messages.push({
                id: Date.now().toString(),
                type: 'assistant',
                content: action.payload.answer,
                timestamp: new Date(),
                sources: action.payload.sources,
                conversationId: action.payload.conversationId
            });
            state.currentConversation = action.payload.conversationId;
            state.error = null;
        }).addCase(sendMessage.rejected, (state, action)=>{
            state.isTyping = false;
            state.error = action.payload;
        })// Fetch conversations
        .addCase(fetchConversations.pending, (state)=>{
            state.isLoading = true;
        }).addCase(fetchConversations.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.conversations = action.payload;
        }).addCase(fetchConversations.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Fetch chat history
        .addCase(fetchChatHistory.pending, (state)=>{
            state.isLoading = true;
        }).addCase(fetchChatHistory.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.messages = action.payload.map((chat)=>[
                    {
                        id: "".concat(chat.id, "-user"),
                        type: 'user',
                        content: chat.query,
                        timestamp: new Date(chat.timestamp),
                        conversationId: chat.conversationId
                    },
                    {
                        id: "".concat(chat.id, "-assistant"),
                        type: 'assistant',
                        content: chat.response,
                        timestamp: new Date(chat.timestamp),
                        sources: chat.sources,
                        conversationId: chat.conversationId
                    }
                ]).flat();
        }).addCase(fetchChatHistory.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Delete conversation
        .addCase(deleteConversation.fulfilled, (state, action)=>{
            state.conversations = state.conversations.filter((conv)=>conv.id !== action.payload);
            if (state.currentConversation === action.payload) {
                state.currentConversation = null;
                state.messages = [];
            }
        })// Search in chat
        .addCase(searchInChat.pending, (state)=>{
            state.isLoading = true;
        }).addCase(searchInChat.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.searchResults = action.payload;
        }).addCase(searchInChat.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        });
    }
});
const { clearError, setCurrentConversation, addUserMessage, setTyping, clearMessages, clearSearchResults, updateMessage } = chatSlice.actions;
const __TURBOPACK__default__export__ = chatSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/slices/uiSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addToast": ()=>addToast,
    "clearToasts": ()=>clearToasts,
    "closeAllModals": ()=>closeAllModals,
    "closeModal": ()=>closeModal,
    "default": ()=>__TURBOPACK__default__export__,
    "openModal": ()=>openModal,
    "removeToast": ()=>removeToast,
    "setLoading": ()=>setLoading,
    "setSidebarOpen": ()=>setSidebarOpen,
    "setTheme": ()=>setTheme,
    "setViewMode": ()=>setViewMode,
    "toggleSidebar": ()=>toggleSidebar,
    "toggleTheme": ()=>toggleTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    theme: 'light',
    sidebarOpen: false,
    toasts: [],
    modals: [],
    isLoading: false,
    loadingMessage: '',
    viewMode: 'grid'
};
const uiSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'ui',
    initialState,
    reducers: {
        setTheme: (state, action)=>{
            state.theme = action.payload;
        },
        toggleTheme: (state)=>{
            state.theme = state.theme === 'light' ? 'dark' : 'light';
        },
        setSidebarOpen: (state, action)=>{
            state.sidebarOpen = action.payload;
        },
        toggleSidebar: (state)=>{
            state.sidebarOpen = !state.sidebarOpen;
        },
        addToast: (state, action)=>{
            const toast = {
                id: Date.now().toString(),
                duration: 5000,
                ...action.payload
            };
            state.toasts.push(toast);
        },
        removeToast: (state, action)=>{
            state.toasts = state.toasts.filter((toast)=>toast.id !== action.payload);
        },
        clearToasts: (state)=>{
            state.toasts = [];
        },
        openModal: (state, action)=>{
            const modal = {
                id: Date.now().toString(),
                ...action.payload
            };
            state.modals.push(modal);
        },
        closeModal: (state, action)=>{
            state.modals = state.modals.filter((modal)=>modal.id !== action.payload);
        },
        closeAllModals: (state)=>{
            state.modals = [];
        },
        setLoading: (state, action)=>{
            state.isLoading = action.payload.isLoading;
            state.loadingMessage = action.payload.message || '';
        },
        setViewMode: (state, action)=>{
            state.viewMode = action.payload;
        }
    }
});
const { setTheme, toggleTheme, setSidebarOpen, toggleSidebar, addToast, removeToast, clearToasts, openModal, closeModal, closeAllModals, setLoading, setViewMode } = uiSlice.actions;
const __TURBOPACK__default__export__ = uiSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "persistor": ()=>persistor,
    "store": ()=>store
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux/dist/redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistStore.js [app-client] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistReducer.js [app-client] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/constants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/lib/storage/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/authSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$documentsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/documentsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$chatSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/chatSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/uiSlice.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
const persistConfig = {
    key: 'root',
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    whitelist: [
        'auth'
    ]
};
const rootReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["combineReducers"])({
    auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    documents: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$documentsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    chat: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$chatSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
const persistedReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(persistConfig, rootReducer);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLUSH"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REHYDRATE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PAUSE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERSIST"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PURGE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGISTER"]
                ]
            }
        })
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReduxProvider": ()=>ReduxProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/integration/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/store.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
function ReduxProvider(param) {
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PersistGate"], {
            loading: null,
            persistor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persistor"],
            children: children
        }, void 0, false, {
            fileName: "[project]/src/lib/redux/provider.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/lib/redux/provider.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = ReduxProvider;
var _c;
__turbopack_context__.k.register(_c, "ReduxProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_lib_346ef07e._.js.map