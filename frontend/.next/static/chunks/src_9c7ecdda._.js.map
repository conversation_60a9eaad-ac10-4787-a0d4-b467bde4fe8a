{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/layout/ProtectedLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Home,\n  MessageSquare,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  User,\n  Sun,\n  Moon\n} from 'lucide-react';\n\ninterface ProtectedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [darkMode, setDarkMode] = useState(false);\n\n  const navigation = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: Home,\n      description: 'Overview & Analytics'\n    },\n    {\n      name: 'Legal Assistant',\n      href: '/legal-assistant',\n      icon: MessageSquare,\n      description: 'AI Chat & Documents'\n    },\n    {\n      name: 'Settings',\n      href: '/settings',\n      icon: Settings,\n      description: 'System Configuration'\n    }\n  ];\n\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n  };\n\n  const handleLogout = () => {\n    router.push('/login');\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${darkMode ? 'dark' : ''}`}>\n      {/* Mobile sidebar backdrop */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Desktop Layout */}\n      <div className=\"hidden lg:flex lg:h-screen lg:overflow-hidden\">\n        {/* Desktop Sidebar */}\n        <aside className=\"w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 flex-shrink-0 overflow-y-auto\">\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => router.push(item.href)}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive \n                          ? 'bg-white/20 dark:bg-gray-900/20' \n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive \n                            ? 'text-white/70 dark:text-gray-900/70' \n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </aside>\n\n        {/* Desktop Main content */}\n        <div className=\"flex-1 flex flex-col min-w-0 overflow-hidden\">\n          {/* Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm sm:px-6 lg:px-8\">\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Page content */}\n          <main className=\"flex-1 overflow-y-auto py-6 px-4 sm:px-6 lg:px-8 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n\n      {/* Mobile Layout */}\n      <div className=\"lg:hidden\">\n        {/* Mobile Sidebar */}\n        <motion.aside\n          initial={false}\n          animate={{\n            x: sidebarOpen ? 0 : -320,\n          }}\n          transition={{ type: 'spring', damping: 30, stiffness: 300 }}\n          className=\"fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 overflow-y-auto\"\n        >\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => {\n                        router.push(item.href);\n                        setSidebarOpen(false);\n                      }}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive\n                          ? 'bg-white/20 dark:bg-gray-900/20'\n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive\n                            ? 'text-white/70 dark:text-gray-900/70'\n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </motion.aside>\n\n        {/* Mobile Main content */}\n        <div className=\"min-h-screen\">\n          {/* Mobile Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm\">\n            <button\n              type=\"button\"\n              className=\"p-2.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center ml-4\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Mobile Page content */}\n          <main className=\"py-6 px-4 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProtectedLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBA,MAAM,kBAAkD;QAAC,EAAE,QAAQ,EAAE;;IACnE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;KACD;IAED,MAAM,iBAAiB;QACrB,YAAY,CAAC;IACf;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAkE,OAAvB,WAAW,SAAS;;0BAE9E,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,eAAe;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;0DAEpE,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;;;;;;;;;;;;8CAOpE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sEAEpC,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAtBhB,KAAK,IAAI;;;;;4CA2BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,SAAS;wBACT,SAAS;4BACP,GAAG,cAAc,IAAI,CAAC;wBACxB;wBACA,YAAY;4BAAE,MAAM;4BAAU,SAAS;4BAAI,WAAW;wBAAI;wBAC1D,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAkD;;;;;;;;;;;8DAEpE,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;;;;;;;sDAIlE,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS;wDACP,OAAO,IAAI,CAAC,KAAK,IAAI;wDACrB,eAAe;oDACjB;;sEAEA,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAzBhB,KAAK,IAAI;;;;;4CA8BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtWM;;QACW,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFxB;uCAwWS", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/app/knowledge-base/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Search,\n  Upload,\n  FileText,\n  MessageSquare,\n  Filter,\n  Grid,\n  List,\n  Calendar,\n  User,\n  Download,\n  Eye,\n  Trash2,\n  Loader2,\n  AlertCircle\n} from 'lucide-react';\nimport ProtectedLayout from '@/components/layout/ProtectedLayout';\nimport { apiService, knowledgeBaseService } from '@/lib/services/apiService';\n\ninterface Document {\n  _id: string;\n  filename: string;\n  file_size: number;\n  uploaded_at: string;\n  status: string;\n  pages?: number;\n  file_type?: string;\n  minio_path?: string;\n}\n\ninterface DocumentsResponse {\n  documents?: Document[];\n  total?: number;\n}\n\nconst KnowledgeBasePage = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [selectedFilter, setSelectedFilter] = useState('all');\n  const [documents, setDocuments] = useState<Document[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalDocuments, setTotalDocuments] = useState(0);\n  const [searchResults, setSearchResults] = useState<any[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  const documentsPerPage = 12;\n\n  // Fetch documents from backend using paginated search\n  const fetchDocuments = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await apiService.searchAllDocuments({\n        query: searchQuery || undefined,\n        page: currentPage,\n        page_size: documentsPerPage,\n        document_type: selectedFilter !== 'all' ? selectedFilter : undefined\n      });\n\n      // Convert search results to document format\n      const documentsFromSearch = response.results?.map((result: any) => ({\n        _id: result.document_id,\n        filename: result.filename,\n        file_size: 0, // Not available in search results\n        uploaded_at: new Date().toISOString(), // Placeholder\n        status: 'processed',\n        pages: result.metadata?.total_pages || 0,\n        file_type: result.metadata?.file_type || 'pdf'\n      })) || [];\n\n      // Remove duplicates by document ID\n      const uniqueDocuments = documentsFromSearch.filter((doc: any, index: number, self: any[]) =>\n        index === self.findIndex((d: any) => d._id === doc._id)\n      );\n\n      setDocuments(uniqueDocuments);\n      setTotalDocuments(response.total || 0);\n      setSearchResults(response.results || []);\n    } catch (err) {\n      console.error('Error fetching documents:', err);\n      setError('Failed to load documents. Please try again.');\n      setDocuments([]);\n      setSearchResults([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, documentsPerPage, searchQuery, selectedFilter]);\n\n  // Search documents using vector search (for real-time search)\n  const searchDocuments = useCallback(async (query: string) => {\n    if (!query.trim()) {\n      setSearchResults([]);\n      setIsSearching(false);\n      fetchDocuments(); // Load all documents when search is cleared\n      return;\n    }\n\n    setIsSearching(true);\n    try {\n      const response = await apiService.searchAllDocuments({\n        query: query,\n        page: 1,\n        page_size: 20, // Show more results for search\n        document_type: selectedFilter !== 'all' ? selectedFilter : undefined\n      });\n\n      setSearchResults(response.results || []);\n\n      // Also update documents list with unique documents from search\n      const documentsFromSearch = response.results?.map((result: any) => ({\n        _id: result.document_id,\n        filename: result.filename,\n        file_size: 0,\n        uploaded_at: new Date().toISOString(),\n        status: 'processed',\n        pages: result.metadata?.total_pages || 0,\n        file_type: result.metadata?.file_type || 'pdf'\n      })) || [];\n\n      const uniqueDocuments = documentsFromSearch.filter((doc: any, index: number, self: any[]) =>\n        index === self.findIndex((d: any) => d._id === doc._id)\n      );\n\n      setDocuments(uniqueDocuments);\n      setTotalDocuments(response.total || 0);\n    } catch (err) {\n      console.error('Error searching documents:', err);\n      setSearchResults([]);\n    } finally {\n      setIsSearching(false);\n    }\n  }, [selectedFilter, fetchDocuments]);\n\n  // Handle file upload\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      await apiService.uploadDocuments(Array.from(files), (progress: number) => {\n        setUploadProgress(progress);\n      });\n\n      // Refresh document list after upload\n      fetchDocuments();\n    } catch (err) {\n      console.error('Error uploading documents:', err);\n      setError('Failed to upload documents. Please try again.');\n    } finally {\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  // Load documents on initial render and when dependencies change\n  useEffect(() => {\n    fetchDocuments();\n  }, [fetchDocuments]);\n\n  // Debounced search\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (searchQuery) {\n        searchDocuments(searchQuery);\n      } else {\n        setSearchResults([]);\n        fetchDocuments();\n      }\n    }, 500);\n\n    return () => clearTimeout(timer);\n  }, [searchQuery, searchDocuments, fetchDocuments]);\n\n  const filters = [\n    { id: 'all', label: 'All Documents' },\n    { id: 'contract', label: 'Contracts' },\n    { id: 'policy', label: 'Policies' },\n    { id: 'agreement', label: 'Agreements' },\n  ];\n\n  // Use documents directly since filtering is done on backend\n  const filteredDocuments = documents;\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 24\n      }\n    }\n  };\n\n  return (\n    <ProtectedLayout>\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3 }}\n          className=\"mb-8\"\n        >\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                Knowledge Base\n              </h1>\n              <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n                Manage and search through your legal documents\n              </p>\n            </div>\n            <motion.label\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              className=\"flex items-center px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors cursor-pointer\"\n            >\n              <Upload className=\"h-4 w-4 mr-2\" />\n              {isUploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Upload Document'}\n              <input\n                type=\"file\"\n                multiple\n                accept=\".pdf,.doc,.docx,.txt\"\n                onChange={handleFileUpload}\n                className=\"hidden\"\n                disabled={isUploading}\n              />\n            </motion.label>\n          </div>\n        </motion.div>\n\n        {/* Search and Filters */}\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.1 }}\n          className=\"bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 mb-8\"\n        >\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search documents...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n\n            {/* Filters */}\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-2\">\n                <Filter className=\"h-4 w-4 text-gray-400\" />\n                <select\n                  value={selectedFilter}\n                  onChange={(e) => setSelectedFilter(e.target.value)}\n                  className=\"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-gray-900\"\n                >\n                  {filters.map(filter => (\n                    <option key={filter.id} value={filter.id}>\n                      {filter.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* View Mode Toggle */}\n              <div className=\"flex items-center border border-gray-300 dark:border-gray-600 rounded-lg\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 ${viewMode === 'grid' ? 'bg-gray-900 text-white' : 'text-gray-400'} rounded-l-lg transition-colors`}\n                >\n                  <Grid className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 ${viewMode === 'list' ? 'bg-gray-900 text-white' : 'text-gray-400'} rounded-r-lg transition-colors`}\n                >\n                  <List className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Documents Grid/List */}\n        <AnimatePresence mode=\"wait\">\n          {viewMode === 'grid' ? (\n            <motion.div\n              key=\"grid\"\n              variants={containerVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              exit=\"hidden\"\n              className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n            >\n              {filteredDocuments.map((doc) => (\n                <motion.div\n                  key={doc._id}\n                  variants={itemVariants}\n                  whileHover={{ scale: 1.02 }}\n                  className=\"bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200\"\n                >\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <FileText className=\"h-8 w-8 text-red-500\" />\n                    <div className=\"flex items-center gap-2\">\n                      <button className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n                        <Download className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"p-1 text-gray-400 hover:text-red-500\">\n                        <Trash2 className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                  \n                  <h3 className=\"font-semibold text-gray-900 dark:text-white mb-2 truncate\">\n                    {doc.filename}\n                  </h3>\n                  \n                  <div className=\"space-y-2 text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center\">\n                      <span className=\"font-medium\">Type:</span>\n                      <span className=\"ml-2\">{doc.file_type || 'PDF'}</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <span className=\"font-medium\">Size:</span>\n                      <span className=\"ml-2\">{doc.file_size ? `${(doc.file_size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <span className=\"font-medium\">Pages:</span>\n                      <span className=\"ml-2\">{doc.pages || 'Unknown'}</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"h-3 w-3 mr-1\" />\n                      <span>{new Date(doc.uploaded_at).toLocaleDateString()}</span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`px-2 py-1 text-xs rounded-full ${\n                        doc.status === 'processed' \n                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'\n                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'\n                      }`}>\n                        {doc.status}\n                      </span>\n                      <button className=\"flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\">\n                        <MessageSquare className=\"h-3 w-3 mr-1\" />\n                        Chat\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"list\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden\"\n            >\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        Document\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        Type\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        Size\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        Uploaded\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                    {filteredDocuments.map((doc, index) => (\n                      <motion.tr\n                        key={doc._id}\n                        initial={{ opacity: 0, x: -10 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: index * 0.1 }}\n                        className=\"hover:bg-gray-50 dark:hover:bg-gray-700\"\n                      >\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <FileText className=\"h-5 w-5 text-red-500 mr-3\" />\n                            <div>\n                              <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                                {doc.filename}\n                              </div>\n                              <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                                {doc.pages || 'Unknown'} pages\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                          {doc.file_type || 'PDF'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                          {doc.file_size ? `${(doc.file_size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                          <div>{new Date(doc.uploaded_at).toLocaleDateString()}</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            doc.status === 'processed' \n                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'\n                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'\n                          }`}>\n                            {doc.status}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex items-center justify-end gap-2\">\n                            <button className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n                              <MessageSquare className=\"h-4 w-4\" />\n                            </button>\n                            <button className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n                              <Download className=\"h-4 w-4\" />\n                            </button>\n                            <button className=\"text-gray-400 hover:text-red-500\">\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"flex items-center justify-center py-12\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-gray-500\" />\n            <span className=\"ml-2 text-gray-500\">Loading documents...</span>\n          </div>\n        )}\n\n        {/* Error State */}\n        {error && (\n          <div className=\"flex items-center justify-center py-12\">\n            <AlertCircle className=\"h-8 w-8 text-red-500 mr-2\" />\n            <span className=\"text-red-500\">{error}</span>\n          </div>\n        )}\n\n        {/* Empty State */}\n        {!loading && !error && filteredDocuments.length === 0 && (\n          <div className=\"text-center py-12\">\n            <FileText className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              No documents found\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {searchQuery ? 'Try adjusting your search query' : 'Upload your first document to get started'}\n            </p>\n          </div>\n        )}\n\n        {/* Pagination */}\n        {!loading && !error && totalDocuments > documentsPerPage && (\n          <div className=\"flex items-center justify-between mt-8 px-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg\">\n            <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n              Showing {((currentPage - 1) * documentsPerPage) + 1} to {Math.min(currentPage * documentsPerPage, totalDocuments)} of {totalDocuments} documents\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\n                disabled={currentPage === 1}\n                className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Previous\n              </button>\n              <span className=\"px-3 py-1 text-sm text-gray-700 dark:text-gray-300\">\n                Page {currentPage} of {Math.ceil(totalDocuments / documentsPerPage)}\n              </span>\n              <button\n                onClick={() => setCurrentPage(prev => Math.min(Math.ceil(totalDocuments / documentsPerPage), prev + 1))}\n                disabled={currentPage >= Math.ceil(totalDocuments / documentsPerPage)}\n                className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Next\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Search Results Info */}\n        {searchResults.length > 0 && searchQuery && (\n          <div className=\"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n            <h4 className=\"font-medium text-blue-900 dark:text-blue-300 mb-2\">\n              Search Results ({searchResults.length} chunks found)\n            </h4>\n            <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n              {searchResults.slice(0, 5).map((result: any, index: number) => (\n                <div key={index} className=\"text-sm\">\n                  <div className=\"font-medium text-blue-800 dark:text-blue-400\">\n                    {result.filename} (Page {result.page_number})\n                  </div>\n                  <div className=\"text-blue-700 dark:text-blue-300 truncate\">\n                    {result.text.substring(0, 150)}...\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </ProtectedLayout>\n  );\n};\n\nexport default KnowledgeBasePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AAAA;;;AArBA;;;;;;AAuCA,MAAM,oBAAoB;;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB;IAEzB,sDAAsD;IACtD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACjC,WAAW;YACX,SAAS;YACT,IAAI;oBAS0B;gBAR5B,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;oBACnD,OAAO,eAAe;oBACtB,MAAM;oBACN,WAAW;oBACX,eAAe,mBAAmB,QAAQ,iBAAiB;gBAC7D;gBAEA,4CAA4C;gBAC5C,MAAM,sBAAsB,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,GAAG;qEAAC,CAAC;4BAM1C,kBACI;+BAPuD;4BAClE,KAAK,OAAO,WAAW;4BACvB,UAAU,OAAO,QAAQ;4BACzB,WAAW;4BACX,aAAa,IAAI,OAAO,WAAW;4BACnC,QAAQ;4BACR,OAAO,EAAA,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,WAAW,KAAI;4BACvC,WAAW,EAAA,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,SAAS,KAAI;wBAC3C;;wEAAO,EAAE;gBAET,mCAAmC;gBACnC,MAAM,kBAAkB,oBAAoB,MAAM;qFAAC,CAAC,KAAU,OAAe,OAC3E,UAAU,KAAK,SAAS;6FAAC,CAAC,IAAW,EAAE,GAAG,KAAK,IAAI,GAAG;;;gBAGxD,aAAa;gBACb,kBAAkB,SAAS,KAAK,IAAI;gBACpC,iBAAiB,SAAS,OAAO,IAAI,EAAE;YACzC,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;gBACT,aAAa,EAAE;gBACf,iBAAiB,EAAE;YACrB,SAAU;gBACR,WAAW;YACb;QACF;wDAAG;QAAC;QAAa;QAAkB;QAAa;KAAe;IAE/D,8DAA8D;IAC9D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO;YACzC,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB,iBAAiB,EAAE;gBACnB,eAAe;gBACf,kBAAkB,4CAA4C;gBAC9D;YACF;YAEA,eAAe;YACf,IAAI;oBAW0B;gBAV5B,MAAM,WAAW,MAAM,uJAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;oBACnD,OAAO;oBACP,MAAM;oBACN,WAAW;oBACX,eAAe,mBAAmB,QAAQ,iBAAiB;gBAC7D;gBAEA,iBAAiB,SAAS,OAAO,IAAI,EAAE;gBAEvC,+DAA+D;gBAC/D,MAAM,sBAAsB,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,GAAG;sEAAC,CAAC;4BAM1C,kBACI;+BAPuD;4BAClE,KAAK,OAAO,WAAW;4BACvB,UAAU,OAAO,QAAQ;4BACzB,WAAW;4BACX,aAAa,IAAI,OAAO,WAAW;4BACnC,QAAQ;4BACR,OAAO,EAAA,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,WAAW,KAAI;4BACvC,WAAW,EAAA,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,SAAS,KAAI;wBAC3C;;yEAAO,EAAE;gBAET,MAAM,kBAAkB,oBAAoB,MAAM;sFAAC,CAAC,KAAU,OAAe,OAC3E,UAAU,KAAK,SAAS;8FAAC,CAAC,IAAW,EAAE,GAAG,KAAK,IAAI,GAAG;;;gBAGxD,aAAa;gBACb,kBAAkB,SAAS,KAAK,IAAI;YACtC,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,iBAAiB,EAAE;YACrB,SAAU;gBACR,eAAe;YACjB;QACF;yDAAG;QAAC;QAAgB;KAAe;IAEnC,qBAAqB;IACrB,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,MAAM,uJAAA,CAAA,aAAU,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC;gBACnD,kBAAkB;YACpB;YAEA,qCAAqC;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,eAAe;YACf,kBAAkB;QACpB;IACF;IAEA,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,QAAQ;qDAAW;oBACvB,IAAI,aAAa;wBACf,gBAAgB;oBAClB,OAAO;wBACL,iBAAiB,EAAE;wBACnB;oBACF;gBACF;oDAAG;YAEH;+CAAO,IAAM,aAAa;;QAC5B;sCAAG;QAAC;QAAa;QAAiB;KAAe;IAEjD,MAAM,UAAU;QACd;YAAE,IAAI;YAAO,OAAO;QAAgB;QACpC;YAAE,IAAI;YAAY,OAAO;QAAY;QACrC;YAAE,IAAI;YAAU,OAAO;QAAW;QAClC;YAAE,IAAI;YAAa,OAAO;QAAa;KACxC;IAED,4DAA4D;IAC5D,MAAM,oBAAoB;IAE1B,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAIvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gCACX,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,cAAc,AAAC,gBAA0C,OAA3B,KAAK,KAAK,CAAC,iBAAgB,OAAK;kDAC/D,6LAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,QAAO;wCACP,UAAU;wCACV,WAAU;wCACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;0DAET,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;wDAAuB,OAAO,OAAO,EAAE;kEACrC,OAAO,KAAK;uDADF,OAAO,EAAE;;;;;;;;;;;;;;;;kDAQ5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,AAAC,OAAuE,OAAjE,aAAa,SAAS,2BAA2B,iBAAgB;0DAEnF,cAAA,6LAAC,4MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,AAAC,OAAuE,OAAjE,aAAa,SAAS,2BAA2B,iBAAgB;0DAEnF,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1B,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,aAAa,uBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,WAAU;kCAET,kBAAkB,GAAG,CAAC,CAAC,oBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKxB,6LAAC;wCAAG,WAAU;kDACX,IAAI,QAAQ;;;;;;kDAGf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAU;kEAAQ,IAAI,SAAS,IAAI;;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAU;kEAAQ,IAAI,SAAS,GAAG,AAAC,GAA2C,OAAzC,CAAC,IAAI,SAAS,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC,IAAG,SAAO;;;;;;;;;;;;0DAE7F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAU;kEAAQ,IAAI,KAAK,IAAI;;;;;;;;;;;;0DAEvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAM,IAAI,KAAK,IAAI,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;kDAIvD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,AAAC,kCAIjB,OAHC,IAAI,MAAM,KAAK,cACX,yEACA;8DAEH,IAAI,MAAM;;;;;;8DAEb,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;+BArD3C,IAAI,GAAG;;;;;uBATZ;;;;iFAuEN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAqG;;;;;;;;;;;;;;;;;kDAKvH,6LAAC;wCAAM,WAAU;kDACd,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDAER,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFACZ,IAAI,QAAQ;;;;;;sFAEf,6LAAC;4EAAI,WAAU;;gFACZ,IAAI,KAAK,IAAI;gFAAU;;;;;;;;;;;;;;;;;;;;;;;;kEAKhC,6LAAC;wDAAG,WAAU;kEACX,IAAI,SAAS,IAAI;;;;;;kEAEpB,6LAAC;wDAAG,WAAU;kEACX,IAAI,SAAS,GAAG,AAAC,GAA2C,OAAzC,CAAC,IAAI,SAAS,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC,IAAG,SAAO;;;;;;kEAEtE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;sEAAK,IAAI,KAAK,IAAI,WAAW,EAAE,kBAAkB;;;;;;;;;;;kEAEpD,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,AAAC,kCAIjB,OAHC,IAAI,MAAM,KAAK,cACX,yEACA;sEAEH,IAAI,MAAM;;;;;;;;;;;kEAGf,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;;;;;;8EAE3B,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAjDnB,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;uBAjClB;;;;;;;;;;gBAgGT,yBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAK,WAAU;sCAAqB;;;;;;;;;;;;gBAKxC,uBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;gBAKnC,CAAC,WAAW,CAAC,SAAS,kBAAkB,MAAM,KAAK,mBAClD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;sCACV,cAAc,oCAAoC;;;;;;;;;;;;gBAMxD,CAAC,WAAW,CAAC,SAAS,iBAAiB,kCACtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAA6D;gCAChE,CAAC,cAAc,CAAC,IAAI,mBAAoB;gCAAE;gCAAK,KAAK,GAAG,CAAC,cAAc,kBAAkB;gCAAgB;gCAAK;gCAAe;;;;;;;sCAExI,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;oCACzD,UAAU,gBAAgB;oCAC1B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAK,WAAU;;wCAAqD;wCAC7D;wCAAY;wCAAK,KAAK,IAAI,CAAC,iBAAiB;;;;;;;8CAEpD,6LAAC;oCACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,iBAAiB,mBAAmB,OAAO;oCACpG,UAAU,eAAe,KAAK,IAAI,CAAC,iBAAiB;oCACpD,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAQN,cAAc,MAAM,GAAG,KAAK,6BAC3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAoD;gCAC/C,cAAc,MAAM;gCAAC;;;;;;;sCAExC,6LAAC;4BAAI,WAAU;sCACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAa,sBAC3C,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,QAAQ;gDAAC;gDAAQ,OAAO,WAAW;gDAAC;;;;;;;sDAE9C,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;gDAAK;;;;;;;;mCALzB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1B;GA3gBM;KAAA;uCA6gBS", "debugId": null}}]}