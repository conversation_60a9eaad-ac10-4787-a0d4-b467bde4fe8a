{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/layout/PublicLayout.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nconst PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex flex-col\">\n      <header className=\"py-4 px-6 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ duration: 0.3 }}\n            >\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                Legal Document System\n              </h1>\n            </motion.div>\n          </div>\n        </div>\n      </header>\n      \n      <main className=\"flex-grow flex items-center justify-center p-6\">\n        <motion.div\n          initial={{ y: 10, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.4 }}\n          className=\"w-full max-w-md\"\n        >\n          {children}\n        </motion.div>\n      </main>\n      \n      <footer className=\"py-4 px-6 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto text-center text-sm text-gray-500 dark:text-gray-400\">\n          &copy; {new Date().getFullYear()} Legal Document System. All rights reserved.\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default PublicLayout;\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,eAA4C;QAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;wBAAI,SAAS;oBAAE;oBAC7B,SAAS;wBAAE,GAAG;wBAAG,SAAS;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAET;;;;;;;;;;;0BAIL,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;wBAAyE;wBAC9E,IAAI,OAAO,WAAW;wBAAG;;;;;;;;;;;;;;;;;;AAK3C;KArCM;uCAuCS", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Eye, EyeOff, LogIn } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { loginUser } from '@/lib/redux/slices/authSlice';\nimport { RootState, AppDispatch } from '@/lib/redux/store';\nimport PublicLayout from '@/components/layout/PublicLayout';\n\nconst LoginPage = () => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const router = useRouter();\n  const dispatch = useDispatch<AppDispatch>();\n  const { status, error: authError } = useSelector((state: RootState) => state.auth);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    try {\n      if (!formData.username || !formData.password) {\n        setError('Please enter both username and password');\n        setIsLoading(false);\n        return;\n      }\n\n      const resultAction = await dispatch(loginUser({\n        username: formData.username,\n        password: formData.password\n      }));\n\n      if (loginUser.fulfilled.match(resultAction)) {\n        // Login successful\n        router.push('/dashboard');\n      } else if (loginUser.rejected.match(resultAction)) {\n        // Login failed\n        setError(resultAction.payload as string || 'Login failed. Please try again.');\n      }\n    } catch (err) {\n      setError('Login failed. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <PublicLayout>\n      <motion.div\n        initial={{ scale: 0.95, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n        className=\"bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-8\"\n      >\n        <div className=\"text-center mb-8\">\n          <motion.div\n            initial={{ y: -10, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.1 }}\n          >\n            <LogIn className=\"mx-auto h-12 w-12 text-gray-900 dark:text-white mb-4\" />\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              Welcome Back\n            </h2>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n              Sign in to your account\n            </p>\n          </motion.div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <motion.div\n            initial={{ x: -10, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Username\n            </label>\n            <input\n              id=\"username\"\n              name=\"username\"\n              type=\"text\"\n              required\n              value={formData.username}\n              onChange={handleInputChange}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200\"\n              placeholder=\"Enter your username\"\n            />\n          </motion.div>\n\n          <motion.div\n            initial={{ x: -10, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ delay: 0.3 }}\n          >\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Password\n            </label>\n            <div className=\"relative\">\n              <input\n                id=\"password\"\n                name=\"password\"\n                type={showPassword ? 'text' : 'password'}\n                required\n                value={formData.password}\n                onChange={handleInputChange}\n                className=\"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200\"\n                placeholder=\"Enter your password\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n              >\n                {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n              </button>\n            </div>\n          </motion.div>\n\n          {error && (\n            <motion.div\n              initial={{ opacity: 0, y: -10 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"text-red-600 dark:text-red-400 text-sm text-center bg-red-50 dark:bg-red-900/20 p-3 rounded-lg\"\n            >\n              {error}\n            </motion.div>\n          )}\n\n          <motion.button\n            type=\"submit\"\n            disabled={isLoading}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            initial={{ y: 10, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.4 }}\n            className=\"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n          >\n            {isLoading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                Signing in...\n              </div>\n            ) : (\n              'Sign in'\n            )}\n          </motion.button>\n        </form>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5 }}\n          className=\"mt-6 text-center\"\n        >\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            Demo credentials: Any username and password\n          </p>\n        </motion.div>\n      </motion.div>\n    </PublicLayout>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;;;AATA;;;;;;;;AAWA,MAAM,YAAY;;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;iCAAE,CAAC,QAAqB,MAAM,IAAI;;IAEjF,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,EAAE;gBAC5C,SAAS;gBACT,aAAa;gBACb;YACF;YAEA,MAAM,eAAe,MAAM,SAAS,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE;gBAC5C,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;YAC7B;YAEA,IAAI,6IAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe;gBAC3C,mBAAmB;gBACnB,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,6IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe;gBACjD,eAAe;gBACf,SAAS,aAAa,OAAO,IAAc;YAC7C;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,UAAY;kBACX,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,OAAO;gBAAM,SAAS;YAAE;YACnC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC,2MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;;;;;;8BAMzD,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG,CAAC;gCAAI,SAAS;4BAAE;4BAC9B,SAAS;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAkE;;;;;;8CAGtG,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG,CAAC;gCAAI,SAAS;4BAAE;4BAC9B,SAAS;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAkE;;;;;;8CAGtG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAM,eAAe,SAAS;4CAC9B,QAAQ;4CACR,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;sDAEd,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;sDAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;yGAAe,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAKrE,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;sCAET;;;;;;sCAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,MAAK;4BACL,UAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,SAAS;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAET,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;;;;;2EAIxF;;;;;;;;;;;;8BAKN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BAEV,cAAA,6LAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;;;;;;;;;;;AAOlE;GA1KM;;QAQW,qIAAA,CAAA,YAAS;QACP,4JAAA,CAAA,cAAW;QACS,4JAAA,CAAA,cAAW;;;KAV5C;uCA4KS", "debugId": null}}]}