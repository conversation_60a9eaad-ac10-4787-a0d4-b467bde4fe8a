(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/redux/slices/authSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAuth": ()=>clearAuth,
    "clearError": ()=>clearError,
    "default": ()=>__TURBOPACK__default__export__,
    "getCurrentUser": ()=>getCurrentUser,
    "loginUser": ()=>loginUser,
    "logoutUser": ()=>logoutUser,
    "setToken": ()=>setToken
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
};
const loginUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/login', async (credentials, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await apiService.login(credentials);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Login failed');
    }
});
const logoutUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/logout', async (_, param)=>{
    let { rejectWithValue } = param;
    try {
        await apiService.logout();
        return null;
    } catch (error) {
        return rejectWithValue(error.message || 'Logout failed');
    }
});
const getCurrentUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/getCurrentUser', async (_, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await apiService.getCurrentUser();
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to get user');
    }
});
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'auth',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setToken: (state, action)=>{
            state.token = action.payload;
            state.isAuthenticated = true;
        },
        clearAuth: (state)=>{
            state.user = null;
            state.token = null;
            state.isAuthenticated = false;
            state.error = null;
        }
    },
    extraReducers: (builder)=>{
        builder// Login
        .addCase(loginUser.pending, (state)=>{
            state.isLoading = true;
            state.error = null;
        }).addCase(loginUser.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.user = action.payload.user;
            state.token = action.payload.token;
            state.isAuthenticated = true;
            state.error = null;
        }).addCase(loginUser.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
            state.isAuthenticated = false;
        })// Logout
        .addCase(logoutUser.pending, (state)=>{
            state.isLoading = true;
        }).addCase(logoutUser.fulfilled, (state)=>{
            state.isLoading = false;
            state.user = null;
            state.token = null;
            state.isAuthenticated = false;
            state.error = null;
        }).addCase(logoutUser.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Get current user
        .addCase(getCurrentUser.pending, (state)=>{
            state.isLoading = true;
        }).addCase(getCurrentUser.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.user = action.payload;
            state.isAuthenticated = true;
            state.error = null;
        }).addCase(getCurrentUser.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
            state.isAuthenticated = false;
        });
    }
});
const { clearError, setToken, clearAuth } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/httpClient.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "httpClient": ()=>httpClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/store.ts [app-client] (ecmascript)");
;
;
class HttpClient {
    getAuthHeaders() {
        var _state_auth;
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"].getState();
        const token = (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : _state_auth.token;
        return token ? {
            'Authorization': "Bearer ".concat(token)
        } : {};
    }
    buildUrl(endpoint, params) {
        const url = new URL("".concat(this.baseURL).concat(endpoint));
        if (params) {
            Object.entries(params).forEach((param)=>{
                let [key, value] = param;
                if (value !== undefined) {
                    url.searchParams.append(key, String(value));
                }
            });
        }
        return url.toString();
    }
    async request(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        const { params, requiresAuth = true, ...fetchOptions } = options;
        const url = this.buildUrl(endpoint, params);
        const headers = new Headers({
            'Content-Type': 'application/json',
            ...fetchOptions.headers,
            ...requiresAuth ? this.getAuthHeaders() : {}
        });
        const response = await fetch(url, {
            ...fetchOptions,
            headers
        });
        // Handle 401 Unauthorized - could trigger logout or token refresh
        if (response.status === 401) {
            console.error('Authentication error: Unauthorized');
        // Could dispatch a logout action here
        // store.dispatch(logout());
        }
        if (!response.ok) {
            const errorData = await response.json().catch(()=>({}));
            throw new Error(errorData.detail || "Request failed with status ".concat(response.status));
        }
        // For 204 No Content
        if (response.status === 204) {
            return {};
        }
        return await response.json();
    }
    async get(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        return this.request(endpoint, {
            ...options,
            method: 'GET'
        });
    }
    async post(endpoint, data) {
        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async put(endpoint, data) {
        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        return this.request(endpoint, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async patch(endpoint, data) {
        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        return this.request(endpoint, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    async delete(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        return this.request(endpoint, {
            ...options,
            method: 'DELETE'
        });
    }
    // Method for file uploads
    async uploadFile(endpoint, file) {
        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        const formData = new FormData();
        formData.append('file', file);
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: formData,
            headers: {} // Let the browser set the content type with boundary
        });
    }
    // Method for multiple file uploads
    async uploadFiles(endpoint, files) {
        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        const formData = new FormData();
        files.forEach((file, index)=>{
            formData.append("file".concat(index), file);
        });
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: formData,
            headers: {} // Let the browser set the content type with boundary
        });
    }
    constructor(baseURL){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "baseURL", void 0);
        this.baseURL = baseURL;
    }
}
const httpClient = new HttpClient(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1');
const __TURBOPACK__default__export__ = HttpClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/authService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": ()=>authService,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/httpClient.ts [app-client] (ecmascript)");
;
class AuthService {
    async login(credentials) {
        const formData = new FormData();
        formData.append('username', credentials.username);
        formData.append('password', credentials.password);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].request('/auth/login', {
            method: 'POST',
            body: formData,
            headers: {},
            requiresAuth: false
        });
        return {
            user: response.user,
            token: response.access_token
        };
    }
    async logout() {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/auth/logout');
    }
    async getCurrentUser() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/auth/me');
    }
    async refreshToken() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/auth/refresh');
        return {
            token: response.access_token
        };
    }
    async changePassword(currentPassword, newPassword) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/auth/change-password', {
            current_password: currentPassword,
            new_password: newPassword
        });
    }
    async resetPassword(email) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/auth/reset-password', {
            email
        }, {
            requiresAuth: false
        });
    }
    async confirmResetPassword(token, newPassword) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/auth/confirm-reset-password', {
            token,
            new_password: newPassword
        }, {
            requiresAuth: false
        });
    }
}
const authService = new AuthService();
const __TURBOPACK__default__export__ = authService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/knowledgeBaseService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "knowledgeBaseService": ()=>knowledgeBaseService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/httpClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/store.ts [app-client] (ecmascript)");
;
;
class KnowledgeBaseService {
    // Custom upload method with progress tracking
    async uploadWithProgress(endpoint, formData, onProgress) {
        if (!onProgress) {
            // If no progress tracking needed, use the httpClient directly
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].request(endpoint, {
                method: 'POST',
                body: formData,
                headers: {} // Let browser set content-type with boundary
            });
        }
        // For progress tracking, we need to use XMLHttpRequest
        return new Promise((resolve, reject)=>{
            const xhr = new XMLHttpRequest();
            xhr.upload.addEventListener('progress', (e)=>{
                if (e.lengthComputable) {
                    const percentComplete = e.loaded / e.total * 100;
                    onProgress(percentComplete);
                }
            });
            xhr.addEventListener('load', ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Invalid JSON response'));
                    }
                } else {
                    try {
                        const errorData = JSON.parse(xhr.responseText);
                        reject(new Error(errorData.detail || "HTTP ".concat(xhr.status, ": ").concat(xhr.statusText)));
                    } catch (error) {
                        reject(new Error("HTTP ".concat(xhr.status, ": ").concat(xhr.statusText)));
                    }
                }
            });
            xhr.addEventListener('error', ()=>{
                reject(new Error('Network error occurred'));
            });
            // Get the base URL
            const baseURL = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1';
            const url = "".concat(baseURL).concat(endpoint);
            xhr.open('POST', url);
            // Get auth token from Redux store
            try {
                var _state_auth;
                const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"].getState();
                const token = (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : _state_auth.token;
                if (token) {
                    xhr.setRequestHeader('Authorization', "Bearer ".concat(token));
                }
            } catch (error) {
                console.warn('Could not get auth token for upload:', error);
            }
            xhr.send(formData);
        });
    }
    // Knowledge Base methods
    async searchAllDocuments(params) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/knowledge-base/search_all', {
            params
        });
    }
    async uploadDocument(file, onProgress) {
        const formData = new FormData();
        formData.append('file', file);
        return this.uploadWithProgress('/knowledge-base/upload', formData, onProgress);
    }
    async uploadDocuments(files, onProgress) {
        const formData = new FormData();
        files.forEach((file, index)=>{
            formData.append("file", file);
        });
        return this.uploadWithProgress('/knowledge-base/upload-multiple', formData, onProgress);
    }
    async getDocuments() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/knowledge-base/documents');
    }
    async deleteDocument(documentId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].delete("/knowledge-base/documents/".concat(documentId));
    }
    async getDocument(documentId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get("/knowledge-base/documents/".concat(documentId));
    }
    async updateDocumentMetadata(documentId, metadata) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].patch("/knowledge-base/documents/".concat(documentId), {
            metadata
        });
    }
}
const knowledgeBaseService = new KnowledgeBaseService();
const __TURBOPACK__default__export__ = knowledgeBaseService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/playgroundService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "playgroundService": ()=>playgroundService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/httpClient.ts [app-client] (ecmascript)");
;
class PlaygroundService {
    // Chat/Message methods
    async sendMessage(request) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/chat/message', request);
    }
    async getConversations() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/chat/conversations');
    }
    async getConversation(conversationId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get("/chat/conversations/".concat(conversationId));
    }
    async getConversationMessages(conversationId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get("/chat/conversations/".concat(conversationId, "/messages"));
    }
    async createConversation(title) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/chat/conversations', {
            title
        });
    }
    async updateConversation(conversationId, title) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].patch("/chat/conversations/".concat(conversationId), {
            title
        });
    }
    async deleteConversation(conversationId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].delete("/chat/conversations/".concat(conversationId));
    }
    async deleteMessage(messageId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].delete("/chat/messages/".concat(messageId));
    }
    // Settings methods
    async getChatSettings() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/chat/settings');
    }
    async updateChatSettings(settings) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].patch('/chat/settings', settings);
    }
    // Model methods
    async getAvailableModels() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/chat/models');
    }
    // Stream message (for real-time chat)
    async streamMessage(request, onChunk) {
        var _response_body;
        const response = await fetch("".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1', "/chat/stream"), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(this.getAuthToken())
            },
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            throw new Error("HTTP ".concat(response.status, ": ").concat(response.statusText));
        }
        const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();
        if (!reader) {
            throw new Error('No response body');
        }
        const decoder = new TextDecoder();
        try {
            while(true){
                const { done, value } = await reader.read();
                if (done) break;
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                for (const line of lines){
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            return;
                        }
                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.content) {
                                onChunk(parsed.content);
                            }
                        } catch (error) {
                            console.warn('Failed to parse SSE data:', data);
                        }
                    }
                }
            }
        } finally{
            reader.releaseLock();
        }
    }
    getAuthToken() {
        try {
            var _state_auth;
            const { store } = __turbopack_context__.r("[project]/src/lib/redux/store.ts [app-client] (ecmascript)");
            const state = store.getState();
            return ((_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : _state_auth.token) || null;
        } catch (error) {
            console.warn('Could not get auth token:', error);
            return null;
        }
    }
}
const playgroundService = new PlaygroundService();
const __TURBOPACK__default__export__ = playgroundService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/dashboardService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "dashboardService": ()=>dashboardService,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/httpClient.ts [app-client] (ecmascript)");
;
class DashboardService {
    // Stats methods
    async getDashboardStats() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/dashboard/stats');
    }
    async getRecentActivity() {
        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/dashboard/activity', {
            params: {
                limit
            }
        });
    }
    async getUsageStats() {
        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/dashboard/usage', {
            params: {
                days
            }
        });
    }
    async getSystemHealth() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/dashboard/health');
    }
    // User preferences
    async getUserPreferences() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/dashboard/preferences');
    }
    async updateUserPreferences(preferences) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].patch('/dashboard/preferences', preferences);
    }
    // Export/Import methods
    async exportData() {
        let format = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'json';
        const response = await fetch("".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1', "/dashboard/export"), {
            method: 'GET',
            headers: {
                'Authorization': "Bearer ".concat(this.getAuthToken()),
                'Accept': format === 'json' ? 'application/json' : 'text/csv'
            }
        });
        if (!response.ok) {
            throw new Error("Export failed: ".concat(response.statusText));
        }
        return response.blob();
    }
    async importData(file) {
        const formData = new FormData();
        formData.append('file', file);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].request('/dashboard/import', {
            method: 'POST',
            body: formData,
            headers: {} // Let browser set content-type with boundary
        });
    }
    // Backup methods
    async createBackup() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].post('/dashboard/backup');
    }
    async getBackups() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].get('/dashboard/backups');
    }
    async downloadBackup(backupId) {
        const response = await fetch("".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1', "/dashboard/backups/").concat(backupId, "/download"), {
            method: 'GET',
            headers: {
                'Authorization': "Bearer ".concat(this.getAuthToken())
            }
        });
        if (!response.ok) {
            throw new Error("Backup download failed: ".concat(response.statusText));
        }
        return response.blob();
    }
    async deleteBackup(backupId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$httpClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["httpClient"].delete("/dashboard/backups/".concat(backupId));
    }
    getAuthToken() {
        try {
            var _state_auth;
            const { store } = __turbopack_context__.r("[project]/src/lib/redux/store.ts [app-client] (ecmascript)");
            const state = store.getState();
            return ((_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : _state_auth.token) || null;
        } catch (error) {
            console.warn('Could not get auth token:', error);
            return null;
        }
    }
}
const dashboardService = new DashboardService();
const __TURBOPACK__default__export__ = dashboardService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Re-export all services for backward compatibility
__turbopack_context__.s({
    "apiService": ()=>apiService,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/authService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/knowledgeBaseService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/playgroundService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$dashboardService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/dashboardService.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
class ApiService {
    constructor(){
        // Auth methods
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "login", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "logout", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getCurrentUser", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"]));
        // Knowledge Base methods
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "searchAllDocuments", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"].searchAllDocuments.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "uploadDocument", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"].uploadDocument.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "uploadDocuments", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"].uploadDocuments.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getDocuments", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"].getDocuments.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "deleteDocument", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"].deleteDocument.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getDocument", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"].getDocument.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["knowledgeBaseService"]));
        // Playground methods
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "sendMessage", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"].sendMessage.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getConversations", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"].getConversations.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getConversation", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"].getConversation.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "deleteConversation", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"].deleteConversation.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["playgroundService"]));
        // Dashboard methods
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getDashboardStats", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$dashboardService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dashboardService"].getDashboardStats.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$dashboardService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dashboardService"]));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getRecentActivity", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$dashboardService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dashboardService"].getRecentActivity.bind(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$dashboardService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dashboardService"]));
    }
}
const apiService = new ApiService();
const __TURBOPACK__default__export__ = apiService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/authService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$knowledgeBaseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/knowledgeBaseService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$playgroundService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/playgroundService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$dashboardService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/dashboardService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <locals>");
}),
"[project]/src/lib/redux/slices/documentsSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearError": ()=>clearError,
    "default": ()=>__TURBOPACK__default__export__,
    "deleteDocument": ()=>deleteDocument,
    "fetchDocuments": ()=>fetchDocuments,
    "getDocumentDetails": ()=>getDocumentDetails,
    "resetUpload": ()=>resetUpload,
    "setFilters": ()=>setFilters,
    "setPagination": ()=>setPagination,
    "setSearchQuery": ()=>setSearchQuery,
    "setSelectedDocument": ()=>setSelectedDocument,
    "setUploadProgress": ()=>setUploadProgress,
    "uploadDocuments": ()=>uploadDocuments
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <locals>");
;
;
const initialState = {
    documents: [],
    selectedDocument: null,
    isLoading: false,
    isUploading: false,
    uploadProgress: 0,
    error: null,
    searchQuery: '',
    filters: {
        type: 'all',
        status: 'all'
    },
    pagination: {
        page: 1,
        limit: 20,
        total: 0
    }
};
const fetchDocuments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/fetchDocuments', async (params, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].getDocuments(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to fetch documents');
    }
});
const uploadDocuments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/uploadDocuments', async (files, param)=>{
    let { rejectWithValue, dispatch } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].uploadDocuments(files, (progress)=>{
            dispatch(setUploadProgress(progress));
        });
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Upload failed');
    }
});
const deleteDocument = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/deleteDocument', async (documentId, param)=>{
    let { rejectWithValue } = param;
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].deleteDocument(documentId);
        return documentId;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to delete document');
    }
});
const getDocumentDetails = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('documents/getDocumentDetails', async (documentId, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].getDocument(documentId);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to get document details');
    }
});
const documentsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'documents',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setSearchQuery: (state, action)=>{
            state.searchQuery = action.payload;
        },
        setFilters: (state, action)=>{
            state.filters = {
                ...state.filters,
                ...action.payload
            };
        },
        setUploadProgress: (state, action)=>{
            state.uploadProgress = action.payload;
        },
        resetUpload: (state)=>{
            state.isUploading = false;
            state.uploadProgress = 0;
        },
        setSelectedDocument: (state, action)=>{
            state.selectedDocument = action.payload;
        },
        setPagination: (state, action)=>{
            state.pagination = {
                ...state.pagination,
                ...action.payload
            };
        }
    },
    extraReducers: (builder)=>{
        builder// Fetch documents
        .addCase(fetchDocuments.pending, (state)=>{
            state.isLoading = true;
            state.error = null;
        }).addCase(fetchDocuments.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.documents = action.payload.documents;
            state.pagination.total = action.payload.total;
            state.error = null;
        }).addCase(fetchDocuments.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Upload documents
        .addCase(uploadDocuments.pending, (state)=>{
            state.isUploading = true;
            state.uploadProgress = 0;
            state.error = null;
        }).addCase(uploadDocuments.fulfilled, (state, action)=>{
            state.isUploading = false;
            state.uploadProgress = 100;
            // Add new documents to the list
            state.documents = [
                ...action.payload.documents,
                ...state.documents
            ];
            state.error = null;
        }).addCase(uploadDocuments.rejected, (state, action)=>{
            state.isUploading = false;
            state.uploadProgress = 0;
            state.error = action.payload;
        })// Delete document
        .addCase(deleteDocument.pending, (state)=>{
            state.isLoading = true;
        }).addCase(deleteDocument.fulfilled, (state, action)=>{
            var _state_selectedDocument;
            state.isLoading = false;
            state.documents = state.documents.filter((doc)=>doc.id !== action.payload);
            if (((_state_selectedDocument = state.selectedDocument) === null || _state_selectedDocument === void 0 ? void 0 : _state_selectedDocument.id) === action.payload) {
                state.selectedDocument = null;
            }
        }).addCase(deleteDocument.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Get document details
        .addCase(getDocumentDetails.pending, (state)=>{
            state.isLoading = true;
        }).addCase(getDocumentDetails.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.selectedDocument = action.payload;
        }).addCase(getDocumentDetails.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        });
    }
});
const { clearError, setSearchQuery, setFilters, setUploadProgress, resetUpload, setSelectedDocument, setPagination } = documentsSlice.actions;
const __TURBOPACK__default__export__ = documentsSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/slices/chatSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addUserMessage": ()=>addUserMessage,
    "clearError": ()=>clearError,
    "clearMessages": ()=>clearMessages,
    "clearSearchResults": ()=>clearSearchResults,
    "default": ()=>__TURBOPACK__default__export__,
    "deleteConversation": ()=>deleteConversation,
    "fetchChatHistory": ()=>fetchChatHistory,
    "fetchConversations": ()=>fetchConversations,
    "searchInChat": ()=>searchInChat,
    "sendMessage": ()=>sendMessage,
    "setCurrentConversation": ()=>setCurrentConversation,
    "setTyping": ()=>setTyping,
    "updateMessage": ()=>updateMessage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/services/apiService.ts [app-client] (ecmascript) <locals>");
;
;
const initialState = {
    conversations: [],
    currentConversation: null,
    messages: [],
    isLoading: false,
    isTyping: false,
    error: null,
    searchResults: []
};
const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/sendMessage', async (params, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].sendChatMessage(params);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to send message');
    }
});
const fetchConversations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/fetchConversations', async (_, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].getChatConversations();
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to fetch conversations');
    }
});
const fetchChatHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/fetchChatHistory', async (conversationId, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].getChatHistory(conversationId);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to fetch chat history');
    }
});
const deleteConversation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/deleteConversation', async (conversationId, param)=>{
    let { rejectWithValue } = param;
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].deleteConversation(conversationId);
        return conversationId;
    } catch (error) {
        return rejectWithValue(error.message || 'Failed to delete conversation');
    }
});
const searchInChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('chat/searchInChat', async (query, param)=>{
    let { rejectWithValue } = param;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$apiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiService"].searchInChat(query);
        return response;
    } catch (error) {
        return rejectWithValue(error.message || 'Search failed');
    }
});
const chatSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'chat',
    initialState,
    reducers: {
        clearError: (state)=>{
            state.error = null;
        },
        setCurrentConversation: (state, action)=>{
            state.currentConversation = action.payload;
        },
        addUserMessage: (state, action)=>{
            state.messages.push(action.payload);
        },
        setTyping: (state, action)=>{
            state.isTyping = action.payload;
        },
        clearMessages: (state)=>{
            state.messages = [];
            state.currentConversation = null;
        },
        clearSearchResults: (state)=>{
            state.searchResults = [];
        },
        updateMessage: (state, action)=>{
            const messageIndex = state.messages.findIndex((msg)=>msg.id === action.payload.id);
            if (messageIndex !== -1) {
                state.messages[messageIndex] = {
                    ...state.messages[messageIndex],
                    ...action.payload.updates
                };
            }
        }
    },
    extraReducers: (builder)=>{
        builder// Send message
        .addCase(sendMessage.pending, (state)=>{
            state.isTyping = true;
            state.error = null;
        }).addCase(sendMessage.fulfilled, (state, action)=>{
            state.isTyping = false;
            state.messages.push({
                id: Date.now().toString(),
                type: 'assistant',
                content: action.payload.answer,
                timestamp: new Date(),
                sources: action.payload.sources,
                conversationId: action.payload.conversationId
            });
            state.currentConversation = action.payload.conversationId;
            state.error = null;
        }).addCase(sendMessage.rejected, (state, action)=>{
            state.isTyping = false;
            state.error = action.payload;
        })// Fetch conversations
        .addCase(fetchConversations.pending, (state)=>{
            state.isLoading = true;
        }).addCase(fetchConversations.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.conversations = action.payload;
        }).addCase(fetchConversations.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Fetch chat history
        .addCase(fetchChatHistory.pending, (state)=>{
            state.isLoading = true;
        }).addCase(fetchChatHistory.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.messages = action.payload.map((chat)=>[
                    {
                        id: "".concat(chat.id, "-user"),
                        type: 'user',
                        content: chat.query,
                        timestamp: new Date(chat.timestamp),
                        conversationId: chat.conversationId
                    },
                    {
                        id: "".concat(chat.id, "-assistant"),
                        type: 'assistant',
                        content: chat.response,
                        timestamp: new Date(chat.timestamp),
                        sources: chat.sources,
                        conversationId: chat.conversationId
                    }
                ]).flat();
        }).addCase(fetchChatHistory.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        })// Delete conversation
        .addCase(deleteConversation.fulfilled, (state, action)=>{
            state.conversations = state.conversations.filter((conv)=>conv.id !== action.payload);
            if (state.currentConversation === action.payload) {
                state.currentConversation = null;
                state.messages = [];
            }
        })// Search in chat
        .addCase(searchInChat.pending, (state)=>{
            state.isLoading = true;
        }).addCase(searchInChat.fulfilled, (state, action)=>{
            state.isLoading = false;
            state.searchResults = action.payload;
        }).addCase(searchInChat.rejected, (state, action)=>{
            state.isLoading = false;
            state.error = action.payload;
        });
    }
});
const { clearError, setCurrentConversation, addUserMessage, setTyping, clearMessages, clearSearchResults, updateMessage } = chatSlice.actions;
const __TURBOPACK__default__export__ = chatSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/slices/uiSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addToast": ()=>addToast,
    "clearToasts": ()=>clearToasts,
    "closeAllModals": ()=>closeAllModals,
    "closeModal": ()=>closeModal,
    "default": ()=>__TURBOPACK__default__export__,
    "openModal": ()=>openModal,
    "removeToast": ()=>removeToast,
    "setLoading": ()=>setLoading,
    "setSidebarOpen": ()=>setSidebarOpen,
    "setTheme": ()=>setTheme,
    "setViewMode": ()=>setViewMode,
    "toggleSidebar": ()=>toggleSidebar,
    "toggleTheme": ()=>toggleTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    theme: 'light',
    sidebarOpen: false,
    toasts: [],
    modals: [],
    isLoading: false,
    loadingMessage: '',
    viewMode: 'grid'
};
const uiSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'ui',
    initialState,
    reducers: {
        setTheme: (state, action)=>{
            state.theme = action.payload;
        },
        toggleTheme: (state)=>{
            state.theme = state.theme === 'light' ? 'dark' : 'light';
        },
        setSidebarOpen: (state, action)=>{
            state.sidebarOpen = action.payload;
        },
        toggleSidebar: (state)=>{
            state.sidebarOpen = !state.sidebarOpen;
        },
        addToast: (state, action)=>{
            const toast = {
                id: Date.now().toString(),
                duration: 5000,
                ...action.payload
            };
            state.toasts.push(toast);
        },
        removeToast: (state, action)=>{
            state.toasts = state.toasts.filter((toast)=>toast.id !== action.payload);
        },
        clearToasts: (state)=>{
            state.toasts = [];
        },
        openModal: (state, action)=>{
            const modal = {
                id: Date.now().toString(),
                ...action.payload
            };
            state.modals.push(modal);
        },
        closeModal: (state, action)=>{
            state.modals = state.modals.filter((modal)=>modal.id !== action.payload);
        },
        closeAllModals: (state)=>{
            state.modals = [];
        },
        setLoading: (state, action)=>{
            state.isLoading = action.payload.isLoading;
            state.loadingMessage = action.payload.message || '';
        },
        setViewMode: (state, action)=>{
            state.viewMode = action.payload;
        }
    }
});
const { setTheme, toggleTheme, setSidebarOpen, toggleSidebar, addToast, removeToast, clearToasts, openModal, closeModal, closeAllModals, setLoading, setViewMode } = uiSlice.actions;
const __TURBOPACK__default__export__ = uiSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "persistor": ()=>persistor,
    "store": ()=>store
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux/dist/redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistStore.js [app-client] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistReducer.js [app-client] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/constants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/lib/storage/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/authSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$documentsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/documentsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$chatSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/chatSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/slices/uiSlice.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
const persistConfig = {
    key: 'root',
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    whitelist: [
        'auth'
    ]
};
const rootReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["combineReducers"])({
    auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    documents: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$documentsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    chat: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$chatSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
const persistedReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(persistConfig, rootReducer);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLUSH"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REHYDRATE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PAUSE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PERSIST"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PURGE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGISTER"]
                ]
            }
        })
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/redux/provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReduxProvider": ()=>ReduxProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/integration/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/redux/store.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
function ReduxProvider(param) {
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PersistGate"], {
            loading: null,
            persistor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$redux$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persistor"],
            children: children
        }, void 0, false, {
            fileName: "[project]/src/lib/redux/provider.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/lib/redux/provider.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = ReduxProvider;
var _c;
__turbopack_context__.k.register(_c, "ReduxProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_lib_f0dcc025._.js.map