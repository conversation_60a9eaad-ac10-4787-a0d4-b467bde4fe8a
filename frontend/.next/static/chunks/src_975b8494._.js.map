{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/layout/ProtectedLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Home,\n  MessageSquare,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  User,\n  Sun,\n  Moon\n} from 'lucide-react';\n\ninterface ProtectedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [darkMode, setDarkMode] = useState(false);\n\n  const navigation = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: Home,\n      description: 'Overview & Analytics'\n    },\n    {\n      name: 'Legal Assistant',\n      href: '/legal-assistant',\n      icon: MessageSquare,\n      description: 'AI Chat & Documents'\n    },\n    {\n      name: 'Settings',\n      href: '/settings',\n      icon: Settings,\n      description: 'System Configuration'\n    }\n  ];\n\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n  };\n\n  const handleLogout = () => {\n    router.push('/login');\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${darkMode ? 'dark' : ''}`}>\n      {/* Mobile sidebar backdrop */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Desktop Layout */}\n      <div className=\"hidden lg:flex lg:h-screen lg:overflow-hidden\">\n        {/* Desktop Sidebar */}\n        <aside className=\"w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 flex-shrink-0 overflow-y-auto\">\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => router.push(item.href)}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive \n                          ? 'bg-white/20 dark:bg-gray-900/20' \n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive \n                            ? 'text-white/70 dark:text-gray-900/70' \n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </aside>\n\n        {/* Desktop Main content */}\n        <div className=\"flex-1 flex flex-col min-w-0 overflow-hidden\">\n          {/* Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm sm:px-6 lg:px-8\">\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Page content */}\n          <main className=\"flex-1 overflow-y-auto py-6 px-4 sm:px-6 lg:px-8 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n\n      {/* Mobile Layout */}\n      <div className=\"lg:hidden\">\n        {/* Mobile Sidebar */}\n        <motion.aside\n          initial={false}\n          animate={{\n            x: sidebarOpen ? 0 : -320,\n          }}\n          transition={{ type: 'spring', damping: 30, stiffness: 300 }}\n          className=\"fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 overflow-y-auto\"\n        >\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => {\n                        router.push(item.href);\n                        setSidebarOpen(false);\n                      }}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive\n                          ? 'bg-white/20 dark:bg-gray-900/20'\n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive\n                            ? 'text-white/70 dark:text-gray-900/70'\n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </motion.aside>\n\n        {/* Mobile Main content */}\n        <div className=\"min-h-screen\">\n          {/* Mobile Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm\">\n            <button\n              type=\"button\"\n              className=\"p-2.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center ml-4\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Mobile Page content */}\n          <main className=\"py-6 px-4 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProtectedLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBA,MAAM,kBAAkD;QAAC,EAAE,QAAQ,EAAE;;IACnE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;KACD;IAED,MAAM,iBAAiB;QACrB,YAAY,CAAC;IACf;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAkE,OAAvB,WAAW,SAAS;;0BAE9E,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,eAAe;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;0DAEpE,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;;;;;;;;;;;;8CAOpE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sEAEpC,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAtBhB,KAAK,IAAI;;;;;4CA2BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,SAAS;wBACT,SAAS;4BACP,GAAG,cAAc,IAAI,CAAC;wBACxB;wBACA,YAAY;4BAAE,MAAM;4BAAU,SAAS;4BAAI,WAAW;wBAAI;wBAC1D,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAkD;;;;;;;;;;;8DAEpE,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;;;;;;;sDAIlE,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS;wDACP,OAAO,IAAI,CAAC,KAAK,IAAI;wDACrB,eAAe;oDACjB;;sEAEA,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAzBhB,KAAK,IAAI;;;;;4CA8BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtWM;;QACW,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFxB;uCAwWS", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/ChatPlayground.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Send,\n  Bot,\n  User,\n  Loader2,\n  Database,\n  MessageCircle\n} from 'lucide-react';\n\n// Types\nexport type PlaygroundMode = 'retrieve' | 'query';\n\nexport interface Message {\n  id: string;\n  content: string;\n  role: 'user' | 'assistant';\n  timestamp: Date;\n}\n\nexport interface RetrieveResponse {\n  id: string;\n  score: number;\n  text: string;\n  metadata: Record<string, any>;\n}\n\nexport interface QueryResponse {\n  query: string;\n  response: string;\n  source_nodes: RetrieveResponse[];\n}\n\n// Props interface\ninterface ChatPlaygroundProps {\n  messages: Message[];\n  inputMessage: string;\n  isLoading: boolean;\n  playgroundMode: PlaygroundMode;\n  onSendMessage: () => void;\n  onInputChange: (value: string) => void;\n  onModeChange: (mode: PlaygroundMode) => void;\n  onSourceNodesUpdate: (nodes: RetrieveResponse[]) => void;\n  onQueryResponseUpdate: (response: string) => void;\n}\n\nconst ChatPlayground: React.FC<ChatPlaygroundProps> = ({\n  messages,\n  inputMessage,\n  isLoading,\n  playgroundMode,\n  onSendMessage,\n  onInputChange,\n  onModeChange,\n  onSourceNodesUpdate,\n  onQueryResponseUpdate\n}) => {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const chatContainerRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      onSendMessage();\n    }\n  };\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('ne-NP', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"w-1/2 flex flex-col bg-white dark:bg-gray-800\" style={{ height: '80vh' }}>\n      {/* Chat Header */}\n      <div className=\"flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ type: \"spring\", stiffness: 260, damping: 20 }}\n              className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg\"\n            >\n              <Bot className=\"h-7 w-7 text-white\" />\n            </motion.div>\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">कानुनी सहायक</h2>\n              <div className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span>AI-powered Legal Assistant</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Mode Toggle */}\n          <div className=\"flex items-center space-x-3 bg-white dark:bg-gray-700 rounded-lg p-1 shadow-sm\">\n            <button\n              onClick={() => onModeChange('retrieve')}\n              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${\n                playgroundMode === 'retrieve'\n                  ? 'bg-blue-500 text-white shadow-sm'\n                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'\n              }`}\n            >\n              <Database className=\"h-4 w-4\" />\n              <span>Retrieve</span>\n            </button>\n            <button\n              onClick={() => onModeChange('query')}\n              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${\n                playgroundMode === 'query'\n                  ? 'bg-blue-500 text-white shadow-sm'\n                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'\n              }`}\n            >\n              <MessageCircle className=\"h-4 w-4\" />\n              <span>Query</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Chat Messages - Flexible Height with Scroll */}\n      <div\n        ref={chatContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800\"\n        style={{ maxHeight: 'calc(80vh - 200px)', minHeight: '300px' }}\n      >\n        <AnimatePresence mode=\"popLayout\">\n          {messages.map((message, index) => (\n            <motion.div\n              key={message.id}\n              initial={{ opacity: 0, y: 20, scale: 0.95 }}\n              animate={{ opacity: 1, y: 0, scale: 1 }}\n              exit={{ opacity: 0, y: -20, scale: 0.95 }}\n              transition={{\n                type: \"spring\",\n                stiffness: 300,\n                damping: 30,\n                delay: index * 0.05\n              }}\n              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`flex items-start space-x-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                {/* Avatar */}\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  className={`w-10 h-10 rounded-2xl flex items-center justify-center shadow-md ${\n                    message.role === 'user'\n                      ? 'bg-gradient-to-br from-emerald-500 to-green-600'\n                      : 'bg-gradient-to-br from-blue-500 to-indigo-600'\n                  }`}\n                >\n                  {message.role === 'user' ? (\n                    <User className=\"h-5 w-5 text-white\" />\n                  ) : (\n                    <Bot className=\"h-5 w-5 text-white\" />\n                  )}\n                </motion.div>\n\n                {/* Message Content */}\n                <div className={`flex flex-col space-y-2 ${message.role === 'user' ? 'items-end' : 'items-start'}`}>\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    className={`px-6 py-4 rounded-3xl shadow-lg ${\n                      message.role === 'user'\n                        ? 'bg-gradient-to-br from-emerald-500 to-green-600 text-white'\n                        : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600'\n                    }`}\n                  >\n                    <p className=\"text-sm leading-relaxed whitespace-pre-wrap\">\n                      {message.content}\n                    </p>\n                  </motion.div>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2\">\n                    {formatTime(message.timestamp)}\n                  </span>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {/* Loading Indicator */}\n        {isLoading && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex justify-start\"\n          >\n            <div className=\"flex items-start space-x-3 max-w-[80%]\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-md\">\n                <Bot className=\"h-5 w-5 text-white\" />\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 px-6 py-4 rounded-3xl shadow-lg border border-gray-200 dark:border-gray-600\">\n                <div className=\"flex items-center space-x-2\">\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                  >\n                    <Loader2 className=\"h-4 w-4 text-blue-500\" />\n                  </motion.div>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    {playgroundMode === 'retrieve' ? 'स्रोत नोडहरू खोज्दै...' : 'जवाफ तयार गर्दै...'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Auto-scroll anchor */}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input Area */}\n      <div className=\"flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-end space-x-4\">\n          <div className=\"flex-1 relative\">\n            <textarea\n              value={inputMessage}\n              onChange={(e) => onInputChange(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={\n                playgroundMode === 'retrieve' \n                  ? \"स्रोत नोडहरू खोज्नुहोस्...\" \n                  : \"तपाईंको कानुनी प्रश्न सोध्नुहोस्...\"\n              }\n              className=\"w-full px-4 py-3 pr-12 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n              rows={3}\n              disabled={isLoading}\n            />\n          </div>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={onSendMessage}\n            disabled={!inputMessage.trim() || isLoading}\n            className=\"p-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Send className=\"h-5 w-5\" />\n          </motion.button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatPlayground;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAiDA,MAAM,iBAAgD;QAAC,EACrD,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACtB;;IACC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEhD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC7D;QACF;mCAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAgD,OAAO;YAAE,QAAQ;QAAO;;0BAErF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;oCAC1D,WAAU;8CAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,AAAC,uFAIX,OAHC,mBAAmB,aACf,qCACA;;sDAGN,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,AAAC,uFAIX,OAHC,mBAAmB,UACf,qCACA;;sDAGN,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,WAAW;oBAAsB,WAAW;gBAAQ;;kCAE7D,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,OAAO;gCAAK;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,OAAO;gCAAE;gCACtC,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;oCAAI,OAAO;gCAAK;gCACxC,YAAY;oCACV,MAAM;oCACN,WAAW;oCACX,SAAS;oCACT,OAAO,QAAQ;gCACjB;gCACA,WAAW,AAAC,QAAiE,OAA1D,QAAQ,IAAI,KAAK,SAAS,gBAAgB;0CAE7D,cAAA,6LAAC;oCAAI,WAAW,AAAC,0CAA2G,OAAlE,QAAQ,IAAI,KAAK,SAAS,qCAAqC;;sDAEvH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,WAAW,AAAC,oEAIX,OAHC,QAAQ,IAAI,KAAK,SACb,oDACA;sDAGL,QAAQ,IAAI,KAAK,uBAChB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;yGAEhB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAKnB,6LAAC;4CAAI,WAAW,AAAC,2BAAgF,OAAtD,QAAQ,IAAI,KAAK,SAAS,cAAc;;8DACjF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,WAAW,AAAC,mCAIX,OAHC,QAAQ,IAAI,KAAK,SACb,+DACA;8DAGN,cAAA,6LAAC;wDAAE,WAAU;kEACV,QAAQ,OAAO;;;;;;;;;;;8DAGpB,6LAAC;oDAAK,WAAU;8DACb,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;+BA5C9B,QAAQ,EAAE;;;;;;;;;;oBAqDpB,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAS;0DAE5D,cAAA,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAK,WAAU;0DACb,mBAAmB,aAAa,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxE,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY;gCACZ,aACE,mBAAmB,aACf,+BACA;gCAEN,WAAU;gCACV,MAAM;gCACN,UAAU;;;;;;;;;;;sCAGd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;4BACT,UAAU,CAAC,aAAa,IAAI,MAAM;4BAClC,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAlNM;KAAA;uCAoNS", "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/DocumentsBrowser.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  FileText,\n  BookOpen,\n  Clock,\n  User,\n  Loader2,\n  ChevronRight,\n  Info,\n  Download,\n  Share2,\n  Database\n} from 'lucide-react';\n\n// Types\nexport interface SearchResult {\n  id: string;\n  text: string;\n  score: number;\n  filename?: string;\n  document_id: string;\n  page_number?: number;\n  chunk_index: number;\n  metadata: Record<string, any>;\n}\n\nexport interface RetrieveResponse {\n  id: string;\n  score: number;\n  text: string;\n  metadata: Record<string, any>;\n}\n\n// Props interface\ninterface DocumentsBrowserProps {\n  searchResults: SearchResult[];\n  sourceNodes: RetrieveResponse[];\n  searchQuery: string;\n  isSearching: boolean;\n  hasMore: boolean;\n  onLoadMore: () => void;\n}\n\nconst DocumentsBrowser: React.FC<DocumentsBrowserProps> = ({\n  searchResults,\n  sourceNodes,\n  searchQuery,\n  isSearching,\n  hasMore,\n  onLoadMore\n}) => {\n  const loadMoreRef = useRef<HTMLDivElement>(null);\n\n  // Intersection Observer for infinite scroll\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        const target = entries[0];\n        if (target.isIntersecting && hasMore && !isSearching) {\n          onLoadMore();\n        }\n      },\n      {\n        threshold: 0.1,\n        rootMargin: '50px'\n      }\n    );\n\n    if (loadMoreRef.current) {\n      observer.observe(loadMoreRef.current);\n    }\n\n    return () => {\n      if (loadMoreRef.current) {\n        observer.unobserve(loadMoreRef.current);\n      }\n    };\n  }, [hasMore, isSearching, onLoadMore]);\n\n  const formatDate = (dateString: string) => {\n    try {\n      return new Date(dateString).toLocaleDateString('ne-NP', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch {\n      return 'Unknown date';\n    }\n  };\n\n  const displayData = sourceNodes.length > 0 ? sourceNodes : searchResults;\n  const isShowingSourceNodes = sourceNodes.length > 0;\n\n  return (\n    <div className=\"w-1/2 flex flex-col bg-gray-50 dark:bg-gray-900\">\n      {/* Documents Header */}\n      <div className=\"flex-shrink-0 p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ type: \"spring\", stiffness: 260, damping: 20 }}\n              className=\"w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg\"\n            >\n              <BookOpen className=\"h-7 w-7 text-white\" />\n            </motion.div>\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                {isShowingSourceNodes ? 'स्रोत नोडहरू' : 'सम्बन्धित दस्तावेजहरू'}\n              </h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center space-x-2\">\n                <span>\n                  {isShowingSourceNodes \n                    ? `${sourceNodes.length} स्रोत नोडहरू फेला परे`\n                    : `${searchResults.length} दस्तावेजहरू फेला परे`\n                  }\n                </span>\n                {searchQuery && (\n                  <>\n                    <span>•</span>\n                    <span className=\"font-medium\">\"{searchQuery}\"</span>\n                  </>\n                )}\n              </p>\n            </div>\n          </div>\n          {isSearching && (\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n            >\n              <Loader2 className=\"h-6 w-6 text-emerald-500\" />\n            </motion.div>\n          )}\n        </div>\n      </div>\n\n      {/* Documents List - Fixed Height with Scroll */}\n      <div \n        className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800\"\n        style={{ height: 'calc(100vh - 200px)' }} // Fixed height for scrolling\n      >\n        <div className=\"space-y-6\">\n          <AnimatePresence mode=\"popLayout\">\n            {/* Show Source Nodes if available */}\n            {isShowingSourceNodes ? (\n              sourceNodes.map((node, index) => (\n                <motion.div\n                  key={node.id}\n                  initial={{ opacity: 0, y: 30, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: -30, scale: 0.95 }}\n                  transition={{\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30,\n                    delay: index * 0.05\n                  }}\n                  whileHover={{\n                    scale: 1.02,\n                    y: -4,\n                    transition: { type: \"spring\", stiffness: 400, damping: 25 }\n                  }}\n                  className=\"bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 cursor-pointer group\"\n                >\n                  {/* Header */}\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <motion.div\n                        whileHover={{ rotate: 5 }}\n                        className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-md\"\n                      >\n                        <Database className=\"h-5 w-5 text-white\" />\n                      </motion.div>\n                      <div>\n                        <h3 className=\"font-bold text-gray-900 dark:text-white text-base group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\">\n                          स्रोत नोड #{index + 1}\n                        </h3>\n                        <div className=\"flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                          <span>ID: {node.id.substring(0, 8)}...</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Score Badge */}\n                    <motion.div\n                      whileHover={{ scale: 1.1 }}\n                      className=\"px-3 py-1 bg-gradient-to-r from-purple-500 to-indigo-600 text-white text-xs font-bold rounded-full shadow-md\"\n                    >\n                      {(node.score * 100).toFixed(1)}%\n                    </motion.div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"mb-4\">\n                    <p className=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">\n                      {node.text}\n                    </p>\n                  </div>\n\n                  {/* Metadata */}\n                  <div className=\"flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400\">\n                        <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                        <span>Source Node</span>\n                      </div>\n                      {node.metadata?.filename && (\n                        <div className=\"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400\">\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                          <span>{node.metadata.filename}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </motion.div>\n              ))\n            ) : (\n              /* Show Search Results */\n              searchResults.map((result, index) => (\n                <motion.div\n                  key={result.id}\n                  initial={{ opacity: 0, y: 30, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: -30, scale: 0.95 }}\n                  transition={{\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30,\n                    delay: index * 0.05\n                  }}\n                  whileHover={{\n                    scale: 1.02,\n                    y: -4,\n                    transition: { type: \"spring\", stiffness: 400, damping: 25 }\n                  }}\n                  className=\"bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 cursor-pointer group\"\n                >\n                  {/* Header */}\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <motion.div\n                        whileHover={{ rotate: 5 }}\n                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-md\"\n                      >\n                        <FileText className=\"h-5 w-5 text-white\" />\n                      </motion.div>\n                      <div>\n                        <h3 className=\"font-bold text-gray-900 dark:text-white text-base group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\">\n                          {result.filename || result.metadata?.filename || 'Unknown Document'}\n                        </h3>\n                        <div className=\"flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                          <div className=\"flex items-center space-x-1\">\n                            <Clock className=\"h-3 w-3\" />\n                            <span>{result.metadata?.uploaded_at ? formatDate(result.metadata.uploaded_at) : 'Unknown date'}</span>\n                          </div>\n                          <span>•</span>\n                          <div className=\"flex items-center space-x-1\">\n                            <User className=\"h-3 w-3\" />\n                            <span>{result.metadata?.uploaded_by || 'Unknown user'}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Score Badge */}\n                    <motion.div\n                      whileHover={{ scale: 1.1 }}\n                      className=\"px-3 py-1 bg-gradient-to-r from-emerald-500 to-green-600 text-white text-xs font-bold rounded-full shadow-md\"\n                    >\n                      {(result.score * 100).toFixed(1)}%\n                    </motion.div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"mb-4\">\n                    <p className=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed line-clamp-4\">\n                      {result.text}\n                    </p>\n                  </div>\n\n                  {/* Metadata */}\n                  <div className=\"flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        <span>Chunk {result.chunk_index || 0}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400\">\n                        <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                        <span className=\"capitalize\">{result.metadata?.language || 'unknown'}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400\">\n                        <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                        <span className=\"capitalize\">{result.metadata?.type?.replace('_', ' ') || 'document'}</span>\n                      </div>\n                    </div>\n\n                    {/* Action Icons */}\n                    <div className=\"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                      <motion.button\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        className=\"p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                      >\n                        <Info className=\"h-4 w-4\" />\n                      </motion.button>\n                      <motion.button\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        className=\"p-2 text-gray-400 hover:text-emerald-500 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 rounded-lg transition-colors\"\n                      >\n                        <Download className=\"h-4 w-4\" />\n                      </motion.button>\n                      <motion.button\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        className=\"p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors\"\n                      >\n                        <Share2 className=\"h-4 w-4\" />\n                      </motion.button>\n                      <motion.div\n                        whileHover={{ x: 5 }}\n                        className=\"p-2\"\n                      >\n                        <ChevronRight className=\"h-4 w-4 text-gray-400\" />\n                      </motion.div>\n                    </div>\n                  </div>\n                </motion.div>\n              ))\n            )}\n          </AnimatePresence>\n\n          {/* Infinite Scroll Trigger */}\n          {hasMore && !isShowingSourceNodes && (\n            <div ref={loadMoreRef} className=\"py-8 flex justify-center\">\n              {isSearching ? (\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                  className=\"flex items-center space-x-3 text-emerald-600 dark:text-emerald-400\"\n                >\n                  <Loader2 className=\"h-6 w-6\" />\n                  <span className=\"text-sm font-medium\">थप दस्तावेजहरू लोड गर्दै...</span>\n                </motion.div>\n              ) : (\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  className=\"px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-full shadow-lg cursor-pointer\"\n                  onClick={onLoadMore}\n                >\n                  <span className=\"text-sm font-medium\">थप दस्तावेजहरू लोड गर्नुहोस्</span>\n                </motion.div>\n              )}\n            </div>\n          )}\n\n          {/* No Results Message */}\n          {displayData.length === 0 && !isSearching && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"text-center py-12\"\n            >\n              <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FileText className=\"h-8 w-8 text-gray-400\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                कुनै दस्तावेज फेला परेन\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                कृपया फरक खोजशब्द प्रयोग गरेर फेरि प्रयास गर्नुहोस्।\n              </p>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DocumentsBrowser;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AA8CA,MAAM,mBAAoD;QAAC,EACzD,aAAa,EACb,WAAW,EACX,WAAW,EACX,WAAW,EACX,OAAO,EACP,UAAU,EACX;;IACC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,IAAI;8CACnB,CAAC;oBACC,MAAM,SAAS,OAAO,CAAC,EAAE;oBACzB,IAAI,OAAO,cAAc,IAAI,WAAW,CAAC,aAAa;wBACpD;oBACF;gBACF;6CACA;gBACE,WAAW;gBACX,YAAY;YACd;YAGF,IAAI,YAAY,OAAO,EAAE;gBACvB,SAAS,OAAO,CAAC,YAAY,OAAO;YACtC;YAEA;8CAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,SAAS,SAAS,CAAC,YAAY,OAAO;oBACxC;gBACF;;QACF;qCAAG;QAAC;QAAS;QAAa;KAAW;IAErC,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;gBACtD,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;QACF,EAAE,UAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,cAAc,YAAY,MAAM,GAAG,IAAI,cAAc;IAC3D,MAAM,uBAAuB,YAAY,MAAM,GAAG;IAElD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;oCAC1D,WAAU;8CAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,uBAAuB,iBAAiB;;;;;;sDAE3C,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DACE,uBACG,AAAC,GAAqB,OAAnB,YAAY,MAAM,EAAC,4BACtB,AAAC,GAAuB,OAArB,cAAc,MAAM,EAAC;;;;;;gDAG7B,6BACC;;sEACE,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;;gEAAc;gEAAE;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAMrD,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAS;sCAE5D,cAAA,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAO3B,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAsB;0BAEvC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;sCAEnB,uBACC,YAAY,GAAG,CAAC,CAAC,MAAM;oCA6Dd;qDA5DP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;wCAAI,OAAO;oCAAK;oCAC1C,SAAS;wCAAE,SAAS;wCAAG,GAAG;wCAAG,OAAO;oCAAE;oCACtC,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;wCAAI,OAAO;oCAAK;oCACxC,YAAY;wCACV,MAAM;wCACN,WAAW;wCACX,SAAS;wCACT,OAAO,QAAQ;oCACjB;oCACA,YAAY;wCACV,OAAO;wCACP,GAAG,CAAC;wCACJ,YAAY;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;oCAC5D;oCACA,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,QAAQ;4DAAE;4DACxB,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;;wEAAmI;wEACnI,QAAQ;;;;;;;8EAEtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;;4EAAK;4EAAK,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;8DAMzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAI;oDACzB,WAAU;;wDAET,CAAC,KAAK,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAKnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DACV,KAAK,IAAI;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAK;;;;;;;;;;;;oDAEP,EAAA,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,QAAQ,mBACtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAM,KAAK,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;mCA9DhC,KAAK,EAAE;;;;;iCAsEhB,uBAAuB,GACvB,cAAc,GAAG,CAAC,CAAC,QAAQ;oCA8BK,kBAKX,mBAKA,mBA+BmB,mBAIA,uBAAA;qDA1EtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;wCAAI,OAAO;oCAAK;oCAC1C,SAAS;wCAAE,SAAS;wCAAG,GAAG;wCAAG,OAAO;oCAAE;oCACtC,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;wCAAI,OAAO;oCAAK;oCACxC,YAAY;wCACV,MAAM;wCACN,WAAW;wCACX,SAAS;wCACT,OAAO,QAAQ;oCACjB;oCACA,YAAY;wCACV,OAAO;wCACP,GAAG,CAAC;wCACJ,YAAY;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;oCAC5D;oCACA,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,QAAQ;4DAAE;4DACxB,WAAU;sEAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,OAAO,QAAQ,MAAI,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,QAAQ,KAAI;;;;;;8EAEnD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,6LAAC;8FAAM,EAAA,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,WAAW,IAAG,WAAW,OAAO,QAAQ,CAAC,WAAW,IAAI;;;;;;;;;;;;sFAElF,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,6LAAC;8FAAM,EAAA,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,WAAW,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAO/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAI;oDACzB,WAAU;;wDAET,CAAC,OAAO,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAKrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DACV,OAAO,IAAI;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;;wEAAK;wEAAO,OAAO,WAAW,IAAI;;;;;;;;;;;;;sEAErC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAc,EAAA,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,QAAQ,KAAI;;;;;;;;;;;;sEAE7D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAc,EAAA,oBAAA,OAAO,QAAQ,cAAf,yCAAA,wBAAA,kBAAiB,IAAI,cAArB,4CAAA,sBAAuB,OAAO,CAAC,KAAK,SAAQ;;;;;;;;;;;;;;;;;;8DAK9E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;4DACvB,WAAU;sEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;4DACvB,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;4DACvB,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,GAAG;4DAAE;4DACnB,WAAU;sEAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mCAxGzB,OAAO,EAAE;;;;;;;;;;;wBAkHrB,WAAW,CAAC,sCACX,6LAAC;4BAAI,KAAK;4BAAa,WAAU;sCAC9B,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAS;gCAC5D,WAAU;;kDAEV,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;yFAGxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;wBAO7C,YAAY,MAAM,KAAK,KAAK,CAAC,6BAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D;GAnVM;KAAA;uCAqVS", "debugId": null}}, {"offset": {"line": 2405, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/app/legal-assistant/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ProtectedLayout from '@/components/layout/ProtectedLayout';\nimport ChatPlayground, {\n  type PlaygroundMode,\n  type Message,\n  type RetrieveResponse,\n  type QueryResponse\n} from '@/components/ChatPlayground';\nimport DocumentsBrowser, {\n  type SearchResult\n} from '@/components/DocumentsBrowser';\nimport { apiService } from '@/lib/services/apiService';\nimport { useInView } from 'react-intersection-observer';\n\ninterface SearchResponse {\n  results: SearchResult[];\n  total_count: number;\n  page: number;\n  page_size: number;\n  total_pages: number;\n  next_offset?: string;\n  has_more: boolean;\n  query?: string;\n  processing_time_ms: number;\n}\n\nconst LegalAssistantPage = () => {\n  // State management\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'नमस्कार! म तपाईंको कानुनी सहायक हुँ। म तपाईंलाई कानुनी दस्तावेजहरू खोज्न र कानुनी प्रश्नहरूको जवाफ दिन मद्दत गर्न सक्छु। तपाईं के खोज्दै हुनुहुन्छ?',\n      role: 'assistant',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(false);\n  const [nextOffset, setNextOffset] = useState<string | undefined>();\n  const [playgroundMode, setPlaygroundMode] = useState<PlaygroundMode>('query');\n  const [sourceNodes, setSourceNodes] = useState<RetrieveResponse[]>([]);\n  const [queryResponse, setQueryResponse] = useState<string>('');\n\n  // Load initial documents\n  useEffect(() => {\n    loadDocuments();\n  }, []);\n\n  const loadDocuments = async (query?: string, page: number = 1, append: boolean = false) => {\n    setIsSearching(true);\n    try {\n      const response: SearchResponse = await apiService.searchAllDocuments({\n        query: query || undefined,\n        page: page,\n        page_size: 12\n      });\n\n      if (append) {\n        setSearchResults(prev => [...prev, ...response.results]);\n      } else {\n        setSearchResults(response.results);\n      }\n\n      setCurrentPage(page);\n      setHasMore(response.has_more);\n      setNextOffset(response.next_offset);\n    } catch (error) {\n      console.error('Error loading documents:', error);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const loadMoreDocuments = () => {\n    if (hasMore && !isSearching) {\n      loadDocuments(searchQuery, currentPage + 1, true);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: inputMessage,\n      role: 'user',\n      timestamp: new Date()\n    };\n\n    const currentInput = inputMessage;\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    // Clear previous results\n    setSourceNodes([]);\n    setQueryResponse('');\n\n    try {\n      if (playgroundMode === 'retrieve') {\n        // Use retrieve endpoint - only get source nodes\n        const retrieveResults = await apiService.retrieveSourceNodes(currentInput, 5);\n        setSourceNodes(retrieveResults);\n\n        const assistantMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          content: `तपाईंको खोज \"${currentInput}\" को लागि ${retrieveResults.length} वटा सम्बन्धित दस्तावेजहरू फेला परेका छन्। दायाँ तिरका स्रोत नोडहरू हेर्नुहोस्।`,\n          role: 'assistant',\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, assistantMessage]);\n      } else {\n        // Use query endpoint - get AI response with source nodes\n        const queryResult = await apiService.queryWithEngine(currentInput);\n        setQueryResponse(queryResult.response);\n        setSourceNodes(queryResult.source_nodes);\n\n        const assistantMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          content: queryResult.response,\n          role: 'assistant',\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, assistantMessage]);\n      }\n\n      // Also search documents for the right panel\n      if (currentInput.trim()) {\n        setSearchQuery(currentInput);\n        loadDocuments(currentInput, 1, false);\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage: Message = {\n        id: (Date.now() + 2).toString(),\n        content: 'माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।',\n        role: 'assistant',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Component event handlers\n  const handleInputChange = (value: string) => {\n    setInputMessage(value);\n  };\n\n  const handleModeChange = (mode: PlaygroundMode) => {\n    setPlaygroundMode(mode);\n  };\n\n  const handleSourceNodesUpdate = (nodes: RetrieveResponse[]) => {\n    setSourceNodes(nodes);\n  };\n\n  const handleQueryResponseUpdate = (response: string) => {\n    setQueryResponse(response);\n  };\n\n  return (\n    <ProtectedLayout>\n      <div className=\"h-screen flex bg-gray-50 dark:bg-gray-900 overflow-hidden\">\n        {/* Chat Playground Component */}\n        <ChatPlayground\n          messages={messages}\n          inputMessage={inputMessage}\n          isLoading={isLoading}\n          playgroundMode={playgroundMode}\n          onSendMessage={handleSendMessage}\n          onInputChange={handleInputChange}\n          onModeChange={handleModeChange}\n          onSourceNodesUpdate={handleSourceNodesUpdate}\n          onQueryResponseUpdate={handleQueryResponseUpdate}\n        />\n\n        {/* Documents Browser Component */}\n        <DocumentsBrowser\n          searchResults={searchResults}\n          sourceNodes={sourceNodes}\n          searchQuery={searchQuery}\n          isSearching={isSearching}\n          hasMore={hasMore}\n          onLoadMore={loadMoreDocuments}\n        />\n      </div>\n    </ProtectedLayout>\n  );\n};\n\nexport default LegalAssistantPage;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAMA;AAGA;AAAA;;;AAdA;;;;;;AA6BA,MAAM,qBAAqB;;IACzB,mBAAmB;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,gBAAgB,eAAO;YAAgB,wEAAe,GAAG,0EAAkB;QAC/E,eAAe;QACf,IAAI;YACF,MAAM,WAA2B,MAAM,uJAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;gBACnE,OAAO,SAAS;gBAChB,MAAM;gBACN,WAAW;YACb;YAEA,IAAI,QAAQ;gBACV,iBAAiB,CAAA,OAAQ;2BAAI;2BAAS,SAAS,OAAO;qBAAC;YACzD,OAAO;gBACL,iBAAiB,SAAS,OAAO;YACnC;YAEA,eAAe;YACf,WAAW,SAAS,QAAQ;YAC5B,cAAc,SAAS,WAAW;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,WAAW,CAAC,aAAa;YAC3B,cAAc,aAAa,cAAc,GAAG;QAC9C;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,MAAM,eAAe;QACrB,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,yBAAyB;QACzB,eAAe,EAAE;QACjB,iBAAiB;QAEjB,IAAI;YACF,IAAI,mBAAmB,YAAY;gBACjC,gDAAgD;gBAChD,MAAM,kBAAkB,MAAM,uJAAA,CAAA,aAAU,CAAC,mBAAmB,CAAC,cAAc;gBAC3E,eAAe;gBAEf,MAAM,mBAA4B;oBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,SAAS,AAAC,gBAAwC,OAAzB,cAAa,cAAmC,OAAvB,gBAAgB,MAAM,EAAC;oBACzE,MAAM;oBACN,WAAW,IAAI;gBACjB;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;YACjD,OAAO;gBACL,yDAAyD;gBACzD,MAAM,cAAc,MAAM,uJAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACrD,iBAAiB,YAAY,QAAQ;gBACrC,eAAe,YAAY,YAAY;gBAEvC,MAAM,mBAA4B;oBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,SAAS,YAAY,QAAQ;oBAC7B,MAAM;oBACN,WAAW,IAAI;gBACjB;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;YACjD;YAEA,4CAA4C;YAC5C,IAAI,aAAa,IAAI,IAAI;gBACvB,eAAe;gBACf,cAAc,cAAc,GAAG;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;IACpB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,eAAe;IACjB;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;IACnB;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uIAAA,CAAA,UAAc;oBACb,UAAU;oBACV,cAAc;oBACd,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,eAAe;oBACf,cAAc;oBACd,qBAAqB;oBACrB,uBAAuB;;;;;;8BAIzB,6LAAC,yIAAA,CAAA,UAAgB;oBACf,eAAe;oBACf,aAAa;oBACb,aAAa;oBACb,aAAa;oBACb,SAAS;oBACT,YAAY;;;;;;;;;;;;;;;;;AAKtB;GAzKM;KAAA;uCA2KS", "debugId": null}}]}