{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/components/layout/ProtectedLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Home,\n  MessageSquare,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  User,\n  Sun,\n  Moon\n} from 'lucide-react';\n\ninterface ProtectedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [darkMode, setDarkMode] = useState(false);\n\n  const navigation = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: Home,\n      description: 'Overview & Analytics'\n    },\n    {\n      name: 'Legal Assistant',\n      href: '/legal-assistant',\n      icon: MessageSquare,\n      description: 'AI Chat & Documents'\n    },\n    {\n      name: 'Settings',\n      href: '/settings',\n      icon: Settings,\n      description: 'System Configuration'\n    }\n  ];\n\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n  };\n\n  const handleLogout = () => {\n    router.push('/login');\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${darkMode ? 'dark' : ''}`}>\n      {/* Mobile sidebar backdrop */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Desktop Layout */}\n      <div className=\"hidden lg:flex lg:h-screen lg:overflow-hidden\">\n        {/* Desktop Sidebar */}\n        <aside className=\"w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 flex-shrink-0 overflow-y-auto\">\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => router.push(item.href)}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive \n                          ? 'bg-white/20 dark:bg-gray-900/20' \n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive \n                            ? 'text-white/70 dark:text-gray-900/70' \n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </aside>\n\n        {/* Desktop Main content */}\n        <div className=\"flex-1 flex flex-col min-w-0 overflow-hidden\">\n          {/* Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm sm:px-6 lg:px-8\">\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Page content */}\n          <main className=\"flex-1 overflow-y-auto py-6 px-4 sm:px-6 lg:px-8 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n\n      {/* Mobile Layout */}\n      <div className=\"lg:hidden\">\n        {/* Mobile Sidebar */}\n        <motion.aside\n          initial={false}\n          animate={{\n            x: sidebarOpen ? 0 : -320,\n          }}\n          transition={{ type: 'spring', damping: 30, stiffness: 300 }}\n          className=\"fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 overflow-y-auto\"\n        >\n          <div className=\"flex h-full flex-col\">\n            {/* Logo */}\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-800\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white dark:text-gray-900 font-bold text-sm\">LS</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                  Legal System\n                </h1>\n              </div>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6\">\n              <div className=\"space-y-1\">\n                <div className=\"px-3 py-2\">\n                  <h2 className=\"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Navigation\n                  </h2>\n                </div>\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <div\n                      key={item.name}\n                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${\n                        isActive\n                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'\n                      }`}\n                      onClick={() => {\n                        router.push(item.href);\n                        setSidebarOpen(false);\n                      }}\n                    >\n                      <div className={`mr-3 p-1 rounded-lg transition-colors ${\n                        isActive\n                          ? 'bg-white/20 dark:bg-gray-900/20'\n                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'\n                      }`}>\n                        <item.icon className=\"h-5 w-5\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        <div className={`text-xs ${\n                          isActive\n                            ? 'text-white/70 dark:text-gray-900/70'\n                            : 'text-gray-500 dark:text-gray-400'\n                        }`}>\n                          {item.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </nav>\n\n            {/* User section */}\n            <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n              <div className=\"space-y-4\">\n                {/* User Profile */}\n                <div className=\"flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50\">\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white dark:text-gray-900\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      Demo User\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      <EMAIL>\n                    </p>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    {darkMode ? <Sun className=\"h-4 w-4\" /> : <Moon className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">24</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Documents</div>\n                  </div>\n                  <div className=\"p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50\">\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">156</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">Chats</div>\n                  </div>\n                </div>\n\n                {/* Logout Button */}\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          </div>\n        </motion.aside>\n\n        {/* Mobile Main content */}\n        <div className=\"min-h-screen\">\n          {/* Mobile Top bar */}\n          <header className=\"sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm\">\n            <button\n              type=\"button\"\n              className=\"p-2.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n\n            <div className=\"flex flex-1 gap-x-4 self-stretch items-center ml-4\">\n              <div className=\"flex-1\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white capitalize\">\n                  {pathname.split('/').pop() || 'Dashboard'}\n                </h1>\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                <button className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\">\n                  <span className=\"sr-only\">Notifications</span>\n                  <div className=\"relative\">\n                    <div className=\"h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700\"></div>\n                    <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </header>\n\n          {/* Mobile Page content */}\n          <main className=\"py-6 px-4 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none\" />\n            <div className=\"relative z-10\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProtectedLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBA,MAAM,kBAAkD;QAAC,EAAE,QAAQ,EAAE;;IACnE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;KACD;IAED,MAAM,iBAAiB;QACrB,YAAY,CAAC;IACf;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,4CAAkE,OAAvB,WAAW,SAAS;;0BAE9E,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,eAAe;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;0DAEpE,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;;;;;;;;;;;;8CAOpE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sEAEpC,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAtBhB,KAAK,IAAI;;;;;4CA2BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,SAAS;wBACT,SAAS;4BACP,GAAG,cAAc,IAAI,CAAC;wBACxB;wBACA,YAAY;4BAAE,MAAM;4BAAU,SAAS;4BAAI,WAAW;wBAAI;wBAC1D,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAkD;;;;;;;;;;;8DAEpE,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;;;;;;;sDAIlE,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;4CAIjG,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC;oDAEC,WAAW,AAAC,sHAIX,OAHC,WACI,sEACA;oDAEN,SAAS;wDACP,OAAO,IAAI,CAAC,KAAK,IAAI;wDACrB,eAAe;oDACjB;;sEAEA,6LAAC;4DAAI,WAAW,AAAC,yCAIhB,OAHC,WACI,oCACA;sEAEJ,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,KAAK,IAAI;;;;;;8EACvC,6LAAC;oEAAI,WAAW,AAAC,WAIhB,OAHC,WACI,wCACA;8EAEH,KAAK,WAAW;;;;;;;;;;;;;mDAzBhB,KAAK,IAAI;;;;;4CA8BpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoD;;;;;;0EAGjE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;kEAI1D,6LAAC;wDACC,SAAS;wDACT,WAAU;kEAET,yBAAW,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;qHAAe,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsD;;;;;;0EACrE,6LAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DACX,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtWM;;QACW,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFxB;uCAwWS", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Nextai/legal/frontend/src/app/playground/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Send, \n  Bot, \n  User, \n  FileText, \n  Sparkles,\n  Clock,\n  Copy,\n  ThumbsUp,\n  ThumbsDown,\n  RotateCcw\n} from 'lucide-react';\nimport ProtectedLayout from '@/components/layout/ProtectedLayout';\n\ninterface Message {\n  id: string;\n  type: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  sources?: Array<{\n    title: string;\n    page: number;\n    snippet: string;\n  }>;\n}\n\nconst PlaygroundPage = () => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      type: 'assistant',\n      content: 'नमस्कार! म तपाईंको कानुनी कागजातहरूको बारेमा प्रश्नहरूको उत्तर दिन यहाँ छु। कुनै प्रश्न सोध्नुहोस्।',\n      timestamp: new Date(),\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputValue,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputValue('');\n    setIsLoading(true);\n\n    // Simulate AI response\n    setTimeout(() => {\n      const assistantMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: `तपाईंको प्रश्न \"${inputValue}\" को उत्तरमा, मैले सान्दर्भिक कागजातहरूमा खोजी गरेको छु। यहाँ केही जानकारी छ जुन उपयोगी हुन सक्छ।`,\n        timestamp: new Date(),\n        sources: [\n          {\n            title: 'Legal Contract Template.pdf',\n            page: 3,\n            snippet: 'This section contains relevant information about...'\n          },\n          {\n            title: 'Terms of Service.pdf',\n            page: 7,\n            snippet: 'Additional details can be found regarding...'\n          }\n        ]\n      };\n      setMessages(prev => [...prev, assistantMessage]);\n      setIsLoading(false);\n    }, 2000);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const clearChat = () => {\n    setMessages([\n      {\n        id: '1',\n        type: 'assistant',\n        content: 'नमस्कार! म तपाईंको कानुनी कागजातहरूको बारेमा प्रश्नहरूको उत्तर दिन यहाँ छु। कुनै प्रश्न सोध्नुहोस्।',\n        timestamp: new Date(),\n      }\n    ]);\n  };\n\n  return (\n    <ProtectedLayout>\n      <div className=\"max-w-4xl mx-auto h-[calc(100vh-12rem)] flex flex-col\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3 }}\n          className=\"flex justify-between items-center mb-6\"\n        >\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white flex items-center\">\n              <Sparkles className=\"h-8 w-8 mr-3 text-yellow-500\" />\n              AI Playground\n            </h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n              Chat with AI about your legal documents\n            </p>\n          </div>\n          <motion.button\n            onClick={clearChat}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            className=\"flex items-center px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\n            Clear Chat\n          </motion.button>\n        </motion.div>\n\n        {/* Chat Container */}\n        <div className=\"flex-1 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden\">\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-6 space-y-6\">\n            <AnimatePresence>\n              {messages.map((message) => (\n                <motion.div\n                  key={message.id}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -10 }}\n                  transition={{ duration: 0.3 }}\n                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n                >\n                  <div className={`flex max-w-3xl ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    {/* Avatar */}\n                    <div className={`flex-shrink-0 ${message.type === 'user' ? 'ml-3' : 'mr-3'}`}>\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                        message.type === 'user' \n                          ? 'bg-gray-900 text-white' \n                          : 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400'\n                      }`}>\n                        {message.type === 'user' ? <User className=\"h-4 w-4\" /> : <Bot className=\"h-4 w-4\" />}\n                      </div>\n                    </div>\n\n                    {/* Message Content */}\n                    <div className={`flex-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>\n                      <div className={`inline-block p-4 rounded-2xl ${\n                        message.type === 'user'\n                          ? 'bg-gray-900 text-white'\n                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'\n                      }`}>\n                        <p className=\"whitespace-pre-wrap\">{message.content}</p>\n                      </div>\n\n                      {/* Sources */}\n                      {message.sources && (\n                        <motion.div\n                          initial={{ opacity: 0, y: 5 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 0.2 }}\n                          className=\"mt-3 space-y-2\"\n                        >\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400 font-medium\">\n                            Sources:\n                          </p>\n                          {message.sources.map((source, index) => (\n                            <motion.div\n                              key={index}\n                              initial={{ opacity: 0, x: -10 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              transition={{ delay: 0.3 + index * 0.1 }}\n                              className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer\"\n                            >\n                              <div className=\"flex items-start\">\n                                <FileText className=\"h-4 w-4 text-red-500 mr-2 mt-0.5 flex-shrink-0\" />\n                                <div className=\"flex-1\">\n                                  <div className=\"flex items-center justify-between\">\n                                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                                      {source.title}\n                                    </h4>\n                                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                                      Page {source.page}\n                                    </span>\n                                  </div>\n                                  <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">\n                                    {source.snippet}\n                                  </p>\n                                </div>\n                              </div>\n                            </motion.div>\n                          ))}\n                        </motion.div>\n                      )}\n\n                      {/* Message Actions */}\n                      {message.type === 'assistant' && (\n                        <div className=\"flex items-center mt-2 space-x-2\">\n                          <button className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded\">\n                            <Copy className=\"h-3 w-3\" />\n                          </button>\n                          <button className=\"p-1 text-gray-400 hover:text-green-600 rounded\">\n                            <ThumbsUp className=\"h-3 w-3\" />\n                          </button>\n                          <button className=\"p-1 text-gray-400 hover:text-red-600 rounded\">\n                            <ThumbsDown className=\"h-3 w-3\" />\n                          </button>\n                          <div className=\"flex items-center text-xs text-gray-400 ml-2\">\n                            <Clock className=\"h-3 w-3 mr-1\" />\n                            {message.timestamp.toLocaleTimeString()}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </AnimatePresence>\n\n            {/* Loading indicator */}\n            {isLoading && (\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"flex justify-start\"\n              >\n                <div className=\"flex mr-3\">\n                  <div className=\"w-8 h-8 rounded-full bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400 flex items-center justify-center\">\n                    <Bot className=\"h-4 w-4\" />\n                  </div>\n                </div>\n                <div className=\"bg-gray-100 dark:bg-gray-700 rounded-2xl p-4\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Input Area */}\n          <div className=\"border-t border-gray-200 dark:border-gray-700 p-4\">\n            <div className=\"flex items-end space-x-3\">\n              <div className=\"flex-1\">\n                <textarea\n                  value={inputValue}\n                  onChange={(e) => setInputValue(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  placeholder=\"तपाईंको प्रश्न यहाँ लेख्नुहोस्...\"\n                  rows={1}\n                  className=\"w-full resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 focus:ring-2 focus:ring-gray-900 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                  style={{ minHeight: '44px', maxHeight: '120px' }}\n                />\n              </div>\n              <motion.button\n                onClick={handleSendMessage}\n                disabled={!inputValue.trim() || isLoading}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                className=\"flex items-center justify-center w-11 h-11 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                <Send className=\"h-4 w-4\" />\n              </motion.button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ProtectedLayout>\n  );\n};\n\nexport default PlaygroundPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAhBA;;;;;AA8BA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,WAAW;QAErC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,mBAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,AAAC,mBAA6B,OAAX,YAAW;gBACvC,WAAW,IAAI;gBACf,SAAS;oBACP;wBACE,OAAO;wBACP,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,OAAO;wBACP,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;YAC/C,aAAa;QACf,GAAG;IACL;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV;gBACE,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;SACD;IACH;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiC;;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAIvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAM1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4LAAA,CAAA,kBAAe;8CACb,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,AAAC,QAAiE,OAA1D,QAAQ,IAAI,KAAK,SAAS,gBAAgB;sDAE7D,cAAA,6LAAC;gDAAI,WAAW,AAAC,kBAA2E,OAA1D,QAAQ,IAAI,KAAK,SAAS,qBAAqB;;kEAE/E,6LAAC;wDAAI,WAAW,AAAC,iBAA0D,OAA1C,QAAQ,IAAI,KAAK,SAAS,SAAS;kEAClE,cAAA,6LAAC;4DAAI,WAAW,AAAC,yDAIhB,OAHC,QAAQ,IAAI,KAAK,SACb,2BACA;sEAEH,QAAQ,IAAI,KAAK,uBAAS,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;yHAAe,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAK7E,6LAAC;wDAAI,WAAW,AAAC,UAA8D,OAArD,QAAQ,IAAI,KAAK,SAAS,eAAe;;0EACjE,6LAAC;gEAAI,WAAW,AAAC,gCAIhB,OAHC,QAAQ,IAAI,KAAK,SACb,2BACA;0EAEJ,cAAA,6LAAC;oEAAE,WAAU;8EAAuB,QAAQ,OAAO;;;;;;;;;;;4DAIpD,QAAQ,OAAO,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,OAAO;gEAAI;gEACzB,WAAU;;kFAEV,6LAAC;wEAAE,WAAU;kFAAuD;;;;;;oEAGnE,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4EAET,SAAS;gFAAE,SAAS;gFAAG,GAAG,CAAC;4EAAG;4EAC9B,SAAS;gFAAE,SAAS;gFAAG,GAAG;4EAAE;4EAC5B,YAAY;gFAAE,OAAO,MAAM,QAAQ;4EAAI;4EACvC,WAAU;sFAEV,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,iNAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;kGACpB,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;;kHACb,6LAAC;wGAAG,WAAU;kHACX,OAAO,KAAK;;;;;;kHAEf,6LAAC;wGAAK,WAAU;;4GAA2C;4GACnD,OAAO,IAAI;;;;;;;;;;;;;0GAGrB,6LAAC;gGAAE,WAAU;0GACV,OAAO,OAAO;;;;;;;;;;;;;;;;;;2EAlBhB;;;;;;;;;;;4DA4BZ,QAAQ,IAAI,KAAK,6BAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;kFAEtB,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;kFAExB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;2CAnF1C,QAAQ,EAAE;;;;;;;;;;gCA8FpB,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;kEACjG,6LAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ3G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,YAAY;4CACZ,aAAY;4CACZ,MAAM;4CACN,WAAU;4CACV,OAAO;gDAAE,WAAW;gDAAQ,WAAW;4CAAQ;;;;;;;;;;;kDAGnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GA3PM;KAAA;uCA6PS", "debugId": null}}]}